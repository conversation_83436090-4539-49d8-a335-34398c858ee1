package com.karimwahib.passwordgenerator;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.os.Vibrator;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;

public class MainActivity extends AppCompatActivity {

    private WebView webView;
    private Vibrator vibrator;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // إخفاء شريط الحالة والتنقل للحصول على تجربة ملء الشاشة
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        // تهيئة الـ Vibrator
        vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);

        // تهيئة WebView
        webView = findViewById(R.id.webview);
        setupWebView();
        
        // تحميل ملف HTML
        webView.loadUrl("file:///android_asset/index.html");
    }

    @SuppressWarnings("deprecation")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();

        // تفعيل JavaScript
        webSettings.setJavaScriptEnabled(true);

        // تحسينات الأداء
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
        
        // دعم الملفات المحلية
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        
        // تحسينات العرض
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setSupportZoom(false);
        
        // إضافة JavaScript Interface للتفاعل مع التطبيق
        webView.addJavascriptInterface(new WebAppInterface(this), "Android");
        
        // تعيين WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url.startsWith("https://wa.me/")) {
                    // فتح رابط الواتساب في التطبيق الخارجي
                    android.content.Intent intent = new android.content.Intent(android.content.Intent.ACTION_VIEW);
                    intent.setData(android.net.Uri.parse(url));
                    startActivity(intent);
                    return true;
                }
                return false;
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                // يمكن إضافة أي إجراءات بعد تحميل الصفحة
            }
        });
    }

    // فئة للتفاعل بين JavaScript والتطبيق
    public class WebAppInterface {
        Context context;

        WebAppInterface(Context c) {
            context = c;
        }

        @JavascriptInterface
        public void copyToClipboard(String text) {
            ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData clip = ClipData.newPlainText("Password", text);
            clipboard.setPrimaryClip(clip);
            
            // اهتزاز خفيف للتأكيد
            if (vibrator != null && vibrator.hasVibrator()) {
                vibrator.vibrate(50);
            }
            
            // إظهار رسالة تأكيد
            runOnUiThread(() -> 
                Toast.makeText(context, "تم نسخ كلمة المرور!", Toast.LENGTH_SHORT).show()
            );
        }

        @JavascriptInterface
        public void vibrate(int duration) {
            if (vibrator != null && vibrator.hasVibrator()) {
                vibrator.vibrate(duration);
            }
        }

        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> 
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            );
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
