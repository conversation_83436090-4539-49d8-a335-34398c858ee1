{"version": 3, "file": "bufferCount.js", "sources": ["../../../src/internal/operators/bufferCount.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AA2D3C,MAAM,UAAU,WAAW,CAAI,UAAkB,EAAE,mBAA2B,IAAI;IAChF,OAAO,SAAS,2BAA2B,CAAC,MAAqB;QAC/D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAI,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,mBAAmB;IAGvB,YAAoB,UAAkB,EAAU,gBAAwB;QAApD,eAAU,GAAV,UAAU,CAAQ;QAAU,qBAAgB,GAAhB,gBAAgB,CAAQ;QACtE,IAAI,CAAC,gBAAgB,IAAI,UAAU,KAAK,gBAAgB,EAAE;YACxD,IAAI,CAAC,eAAe,GAAG,qBAAqB,CAAC;SAC9C;aAAM;YACL,IAAI,CAAC,eAAe,GAAG,yBAAyB,CAAC;SAClD;IACH,CAAC;IAED,IAAI,CAAC,UAA2B,EAAE,MAAW;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACxG,CAAC;CACF;AAOD,MAAM,qBAAyB,SAAQ,UAAa;IAGlD,YAAY,WAA4B,EAAU,UAAkB;QAClE,KAAK,CAAC,WAAW,CAAC,CAAC;QAD6B,eAAU,GAAV,UAAU,CAAQ;QAF5D,WAAM,GAAQ,EAAE,CAAC;IAIzB,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEnB,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;SAClB;IACH,CAAC;IAES,SAAS;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/B;QACD,KAAK,CAAC,SAAS,EAAE,CAAC;IACpB,CAAC;CACF;AAOD,MAAM,yBAA6B,SAAQ,UAAa;IAItD,YAAY,WAA4B,EAAU,UAAkB,EAAU,gBAAwB;QACpG,KAAK,CAAC,WAAW,CAAC,CAAC;QAD6B,eAAU,GAAV,UAAU,CAAQ;QAAU,qBAAgB,GAAhB,gBAAgB,CAAQ;QAH9F,YAAO,GAAe,EAAE,CAAC;QACzB,UAAK,GAAW,CAAC,CAAC;IAI1B,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QAE9D,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,KAAK,GAAG,gBAAgB,KAAK,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAClB;QAED,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAI;YAClC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;gBAChC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC/B;SACF;IACH,CAAC;IAES,SAAS;QACjB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAEtC,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAI,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC1B;SACF;QACD,KAAK,CAAC,SAAS,EAAE,CAAC;IACpB,CAAC;CAEF"}