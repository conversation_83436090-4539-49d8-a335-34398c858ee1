{"version": 3, "file": "bufferToggle.js", "sources": ["../../../src/internal/operators/bufferToggle.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAkDrD,MAAM,UAAU,YAAY,CAC1B,QAAkC,EAClC,eAAyD;IAEzD,OAAO,SAAS,4BAA4B,CAAC,MAAqB;QAChE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAO,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,oBAAoB;IAExB,YAAoB,QAAkC,EAClC,eAAyD;QADzD,aAAQ,GAAR,QAAQ,CAA0B;QAClC,oBAAe,GAAf,eAAe,CAA0C;IAC7E,CAAC;IAED,IAAI,CAAC,UAA2B,EAAE,MAAW;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvG,CAAC;CACF;AAYD,MAAM,sBAA6B,SAAQ,eAAqB;IAG9D,YAAY,WAA4B,EAC5B,QAAkC,EAC1B,eAAgE;QAClF,KAAK,CAAC,WAAW,CAAC,CAAC;QADD,oBAAe,GAAf,eAAe,CAAiD;QAJ5E,aAAQ,GAA4B,EAAE,CAAC;QAM7C,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC9C,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAChC;IACH,CAAC;IAES,MAAM,CAAC,GAAQ;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAG,CAAC;YAClC,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACnC,OAAO,CAAC,MAAM,GAAG,IAAK,CAAC;YACvB,OAAO,CAAC,YAAY,GAAG,IAAK,CAAC;SAC9B;QACD,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC;QACtB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAES,SAAS;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAG,CAAC;YAClC,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACnC,OAAO,CAAC,MAAM,GAAG,IAAK,CAAC;YACvB,OAAO,CAAC,YAAY,GAAG,IAAK,CAAC;SAC9B;QACD,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC;QACtB,KAAK,CAAC,SAAS,EAAE,CAAC;IACpB,CAAC;IAED,UAAU,CAAC,UAAe,EAAE,UAAa;QACvC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC1E,CAAC;IAED,cAAc,CAAC,QAA+B;QAC5C,IAAI,CAAC,WAAW,CAAQ,QAAS,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEO,UAAU,CAAC,KAAQ;QACzB,IAAI;YACF,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;YAC7C,MAAM,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,eAAe,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;aACpC;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAClB;IACH,CAAC;IAEO,WAAW,CAAC,OAAyB;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,IAAI,QAAQ,IAAI,OAAO,EAAE;YACvB,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;YAC/B,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC1B,YAAY,CAAC,WAAW,EAAE,CAAC;SAC5B;IACH,CAAC;IAEO,YAAY,CAAC,eAAoB;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE/B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACxC,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;QACzC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvB,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,EAAE,eAAe,EAAE,OAAc,CAAC,CAAC;QAEnF,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;YAClD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC3B;aAAM;YACJ,iBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;YAE7C,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC5B,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;SACrC;IACH,CAAC;CACF"}