[{"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-xhdpi_ic_launcher.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-xhdpi/ic_launcher.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable-anydpi-v24_ic_launcher_background.xml.flat", "source": "com.karimwahib.passwordgenerator.app-pngs-28:/drawable-anydpi-v24/ic_launcher_background.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable_ic_launcher_foreground.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/drawable/ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-merged_res-32:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-main-34:\\xml\\backup_rules.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable-hdpi_ic_launcher_background.png.flat", "source": "com.karimwahib.passwordgenerator.app-pngs-28:/drawable-hdpi/ic_launcher_background.png"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-xhdpi/ic_launcher_round.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-mdpi_ic_launcher_round.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-mdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-merged_res-32:\\drawable_ic_app_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-main-34:\\drawable\\ic_app_logo.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable-mdpi_ic_launcher_background.png.flat", "source": "com.karimwahib.passwordgenerator.app-pngs-28:/drawable-mdpi/ic_launcher_background.png"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-xxxhdpi/ic_launcher.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable-xxhdpi_ic_launcher_background.png.flat", "source": "com.karimwahib.passwordgenerator.app-pngs-28:/drawable-xxhdpi/ic_launcher_background.png"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-mdpi_ic_launcher.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-mdpi/ic_launcher.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable-xhdpi_ic_launcher_background.png.flat", "source": "com.karimwahib.passwordgenerator.app-pngs-28:/drawable-xhdpi/ic_launcher_background.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-merged_res-32:\\drawable_splash_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-main-34:\\drawable\\splash_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-merged_res-32:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-main-34:\\drawable\\gradient_background.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable-xxxhdpi_ic_launcher_background.png.flat", "source": "com.karimwahib.passwordgenerator.app-pngs-28:/drawable-xxxhdpi/ic_launcher_background.png"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-xxhdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-merged_res-32:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-main-34:\\layout\\activity_splash.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/drawable-ldpi_ic_launcher_background.png.flat", "source": "com.karimwahib.passwordgenerator.app-pngs-28:/drawable-ldpi/ic_launcher_background.png"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-hdpi_ic_launcher_round.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-hdpi/ic_launcher_round.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-xxhdpi_ic_launcher.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-xxhdpi/ic_launcher.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-hdpi_ic_launcher.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-hdpi/ic_launcher.xml"}, {"merged": "com.karimwahib.passwordgenerator.app-merged_res-32:/mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "com.karimwahib.passwordgenerator.app-main-34:/mipmap-xxxhdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-merged_res-32:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-main-34:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-merged_res-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.karimwahib.passwordgenerator.app-main-34:\\layout\\activity_main.xml"}]