{"cordova-plugin-whitelist": {"source": {"type": "registry", "id": "cordova-plugin-whitelist@^1.3.4"}, "is_top_level": true, "variables": {}}, "cordova-plugin-device": {"source": {"type": "registry", "id": "cordova-plugin-device@^2.0.3"}, "is_top_level": true, "variables": {}}, "cordova-plugin-splashscreen": {"source": {"type": "registry", "id": "cordova-plugin-splashscreen@^5.0.3"}, "is_top_level": true, "variables": {}}, "cordova-plugin-network-information": {"source": {"type": "registry", "id": "cordova-plugin-network-information@^2.0.2"}, "is_top_level": true, "variables": {}}, "cordova-plugin-file": {"source": {"type": "registry", "id": "cordova-plugin-file@^6.0.2"}, "is_top_level": true, "variables": {}}}