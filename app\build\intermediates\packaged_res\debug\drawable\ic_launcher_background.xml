<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- خلفية متدرجة -->
    <path android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient 
                android:startY="0"
                android:startX="0"
                android:endY="108"
                android:endX="108"
                android:type="linear">
                <item android:offset="0" android:color="#667eea"/>
                <item android:offset="1" android:color="#764ba2"/>
            </gradient>
        </aapt:attr>
    </path>
    
    <!-- دوائر زخرفية -->
    <circle
        android:fillColor="#33FFFFFF"
        android:cx="20"
        android:cy="20"
        android:r="8"/>
    <circle
        android:fillColor="#33FFFFFF"
        android:cx="88"
        android:cy="20"
        android:r="6"/>
    <circle
        android:fillColor="#33FFFFFF"
        android:cx="20"
        android:cy="88"
        android:r="6"/>
    <circle
        android:fillColor="#33FFFFFF"
        android:cx="88"
        android:cy="88"
        android:r="8"/>
        
    <!-- خطوط زخرفية -->
    <path
        android:fillColor="#22FFFFFF"
        android:pathData="M30,30 L78,30 L78,78 L30,78 Z"/>
</vector>
