<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد كلمات مرور قوية</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Tajawal', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a237e, #311b92);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #e0e0e0;
            font-weight: 700;
        }
        
        header p {
            color: #b0b0b0;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .password-display {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            position: relative;
            display: flex;
            align-items: center;
        }
        
        #password {
            flex-grow: 1;
            font-size: 20px;
            letter-spacing: 1px;
            color: #ffffff;
            word-break: break-all;
            padding-right: 40px;
            min-height: 60px;
            display: flex;
            align-items: center;
        }
        
        .copy-btn {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: #fff;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }
        
        .options {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .option {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .option:last-child {
            margin-bottom: 0;
        }
        
        .option label {
            font-size: 18px;
            color: #e0e0e0;
            display: flex;
            align-items: center;
        }
        
        .option label i {
            margin-left: 10px;
            font-size: 20px;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .slider-container label {
            font-size: 18px;
            color: #e0e0e0;
            margin-left: 15px;
        }
        
        #length {
            flex-grow: 1;
            height: 8px;
            -webkit-appearance: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            outline: none;
        }
        
        #length::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background: #7c4dff;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(124, 77, 255, 0.5);
        }
        
        #length-value {
            background: rgba(255, 255, 255, 0.15);
            width: 50px;
            text-align: center;
            padding: 5px 10px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 18px;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 30px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.1);
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
            background-color: #7c4dff;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: rgba(124, 77, 255, 0.3);
        }
        
        input:checked + .slider:before {
            transform: translateX(30px);
        }
        
        .strength-meter {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .strength-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .strength-header h3 {
            font-size: 18px;
            color: #e0e0e0;
        }
        
        #strength-value {
            font-weight: 700;
            font-size: 18px;
        }
        
        .meter {
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
        }
        
        .meter-fill {
            height: 100%;
            width: 0%;
            background: #ff1744;
            border-radius: 5px;
            transition: width 0.5s ease, background 0.5s ease;
        }
        
        .generate-btn {
            background: linear-gradient(to right, #7c4dff, #651fff);
            color: white;
            border: none;
            width: 100%;
            padding: 18px;
            font-size: 20px;
            font-weight: 700;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(124, 77, 255, 0.4);
        }
        
        .generate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(124, 77, 255, 0.6);
        }
        
        .generate-btn:active {
            transform: translateY(1px);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 16px;
            display: none;
            animation: fadeInOut 3s;
            z-index: 1000;
        }
        
        .developer {
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            text-align: center;
            margin-top: 15px;
            padding: 10px 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .developer:hover {
            color: rgba(255, 255, 255, 0.8);
            background: rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
        }
        
        .developer span {
            font-weight: bold;
            color: #7c4dff;
        }
        
        .developer-icon {
            font-size: 16px;
            animation: pulse 2s infinite;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateY(-20px); }
            10% { opacity: 1; transform: translateY(0); }
            90% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            
            header h1 {
                font-size: 24px;
            }
            
            header p {
                font-size: 14px;
            }
            
            #password {
                font-size: 18px;
            }
            
            .option label {
                font-size: 16px;
            }
            
            .developer {
                font-size: 13px;
                padding: 8px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>مولد كلمات مرور قوية</h1>
            <p>أنشئ كلمات مرور آمنة وعشوائية تلبي جميع متطلبات الأمان المعقدة لحماية حساباتك</p>
        </header>
        
        <div class="password-display">
            <div id="password">انقر على الزر لإنشاء كلمة مرور</div>
            <button class="copy-btn" id="copy-btn">
                <span class="material-icons">content_copy</span>
            </button>
        </div>
        
        <div class="options">
            <div class="slider-container">
                <input type="range" id="length" min="8" max="32" value="16">
                <label for="length">طول كلمة المرور:</label>
                <div id="length-value">16</div>
            </div>
            
            <div class="option">
                <label for="uppercase"><i class="material-icons">text_fields</i> أحرف كبيرة (A-Z)</label>
                <label class="switch">
                    <input type="checkbox" id="uppercase" checked>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="option">
                <label for="lowercase"><i class="material-icons">text_format</i> أحرف صغيرة (a-z)</label>
                <label class="switch">
                    <input type="checkbox" id="lowercase" checked>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="option">
                <label for="numbers"><i class="material-icons">pin</i> أرقام (0-9)</label>
                <label class="switch">
                    <input type="checkbox" id="numbers" checked>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="option">
                <label for="symbols"><i class="material-icons">star</i> رموز خاصة (@#$%)</label>
                <label class="switch">
                    <input type="checkbox" id="symbols" checked>
                    <span class="slider"></span>
                </label>
            </div>
        </div>
        
        <div class="strength-meter">
            <div class="strength-header">
                <h3>قوة كلمة المرور:</h3>
                <div id="strength-value">متوسط</div>
            </div>
            <div class="meter">
                <div class="meter-fill" id="meter-fill"></div>
            </div>
        </div>
        
        <button class="generate-btn" id="generate-btn">
            <i class="material-icons">autorenew</i> إنشاء كلمة مرور جديدة
        </button>
    </div>
    
    <div class="developer">
        <i class="material-icons developer-icon">code</i>
        Developed by <span>Karim Wahib</span>
    </div>
    
    <div class="notification" id="notification">تم نسخ كلمة المرور إلى الحافظة!</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // عناصر DOM
            const passwordEl = document.getElementById('password');
            const copyBtn = document.getElementById('copy-btn');
            const lengthSlider = document.getElementById('length');
            const lengthValue = document.getElementById('length-value');
            const uppercaseEl = document.getElementById('uppercase');
            const lowercaseEl = document.getElementById('lowercase');
            const numbersEl = document.getElementById('numbers');
            const symbolsEl = document.getElementById('symbols');
            const generateBtn = document.getElementById('generate-btn');
            const strengthValue = document.getElementById('strength-value');
            const meterFill = document.getElementById('meter-fill');
            const notification = document.getElementById('notification');
            
            // تحديث قيمة طول كلمة المرور
            lengthSlider.addEventListener('input', function() {
                lengthValue.textContent = this.value;
            });
            
            // إنشاء كلمة مرور
            function generatePassword() {
                const length = lengthSlider.value;
                const includeUppercase = uppercaseEl.checked;
                const includeLowercase = lowercaseEl.checked;
                const includeNumbers = numbersEl.checked;
                const includeSymbols = symbolsEl.checked;
                
                // التحقق من تحديد خيار واحد على الأقل
                if (!includeUppercase && !includeLowercase && !includeNumbers && !includeSymbols) {
                    showNotification('الرجاء تحديد خيار واحد على الأقل!');
                    return 'الرجاء تحديد خيار واحد على الأقل';
                }
                
                // مجموعات الأحرف
                const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                const lowercase = 'abcdefghijklmnopqrstuvwxyz';
                const numbers = '0123456789';
                const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
                
                // إنشاء مجموعة الأحرف المحددة
                let charSet = '';
                if (includeUppercase) charSet += uppercase;
                if (includeLowercase) charSet += lowercase;
                if (includeNumbers) charSet += numbers;
                if (includeSymbols) charSet += symbols;
                
                // توليد كلمة المرور
                let password = '';
                for (let i = 0; i < length; i++) {
                    const randomIndex = Math.floor(Math.random() * charSet.length);
                    password += charSet[randomIndex];
                }
                
                // تحديث قوة كلمة المرور
                updatePasswordStrength(password);
                
                return password;
            }
            
            // تحديث قوة كلمة المرور
            function updatePasswordStrength(password) {
                let strength = 0;
                
                // طول كلمة المرور
                if (password.length >= 12) strength += 30;
                else if (password.length >= 8) strength += 15;
                
                // تنوع الأحرف
                const hasUppercase = /[A-Z]/.test(password);
                const hasLowercase = /[a-z]/.test(password);
                const hasNumbers = /[0-9]/.test(password);
                const hasSymbols = /[^A-Za-z0-9]/.test(password);
                
                if (hasUppercase) strength += 15;
                if (hasLowercase) strength += 15;
                if (hasNumbers) strength += 15;
                if (hasSymbols) strength += 15;
                
                // تحديث واجهة المستخدم
                meterFill.style.width = strength + '%';
                
                if (strength < 30) {
                    strengthValue.textContent = 'ضعيف';
                    meterFill.style.background = '#ff1744';
                } else if (strength < 70) {
                    strengthValue.textContent = 'متوسط';
                    meterFill.style.background = '#ff9800';
                } else if (strength < 90) {
                    strengthValue.textContent = 'قوي';
                    meterFill.style.background = '#4caf50';
                } else {
                    strengthValue.textContent = 'قوي جداً';
                    meterFill.style.background = '#00e676';
                }
            }
            
            // نسخ كلمة المرور
            function copyPassword() {
                if (passwordEl.textContent === 'انقر على الزر لإنشاء كلمة مرور' || 
                    passwordEl.textContent === 'الرجاء تحديد خيار واحد على الأقل') {
                    return;
                }
                
                navigator.clipboard.writeText(passwordEl.textContent)
                    .then(() => {
                        showNotification('تم نسخ كلمة المرور إلى الحافظة!');
                    })
                    .catch(err => {
                        console.error('فشل في النسخ: ', err);
                    });
            }
            
            // إظهار الإشعار
            function showNotification(message) {
                notification.textContent = message;
                notification.style.display = 'block';
                
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 3000);
            }
            
            // إنشاء كلمة مرور عند النقر على الزر
            generateBtn.addEventListener('click', function() {
                passwordEl.textContent = generatePassword();
            });
            
            // نسخ كلمة المرور عند النقر على زر النسخ
            copyBtn.addEventListener('click', copyPassword);
            
            // إنشاء كلمة مرور أولية عند التحميل
            passwordEl.textContent = generatePassword();
        });
    </script>
</body>
</html>