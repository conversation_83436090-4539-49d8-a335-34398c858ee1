{"version": 3, "file": "catchError.js", "sources": ["../../../src/internal/operators/catchError.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAkFjG,MAAM,UAAU,UAAU,CACxB,QAAgD;IAEhD,OAAO,SAAS,0BAA0B,CAAC,MAAqB;QAC9D,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAuB,CAAC,CAAC;IACrD,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,aAAa;IAGjB,YAAoB,QAAqE;QAArE,aAAQ,GAAR,QAAQ,CAA6D;IACzF,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACvF,CAAC;CACF;AAOD,MAAM,eAAsB,SAAQ,qBAA+B;IACjE,YAAY,WAA4B,EACpB,QAAqE,EACrE,MAAqB;QACvC,KAAK,CAAC,WAAW,CAAC,CAAC;QAFD,aAAQ,GAAR,QAAQ,CAA6D;QACrE,WAAM,GAAN,MAAM,CAAe;IAEzC,CAAC;IAOD,KAAK,CAAC,GAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,MAAW,CAAC;YAChB,IAAI;gBACF,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;aAC1C;YAAC,OAAO,IAAI,EAAE;gBACb,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAClB,OAAO;aACR;YACD,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,MAAM,eAAe,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACxD,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC1B,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YAIlE,IAAI,iBAAiB,KAAK,eAAe,EAAE;gBACzC,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;aAC7B;SACF;IACH,CAAC;CACF"}