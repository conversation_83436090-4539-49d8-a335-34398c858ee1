{"prepare_queue": {"installed": [], "uninstalled": []}, "config_munge": {"files": {"res/xml/config.xml": {"parents": {"/*": [{"xml": "<feature name=\"Device\"><param name=\"android-package\" value=\"org.apache.cordova.device.Device\" /></feature>", "count": 1}, {"xml": "<feature name=\"SplashScreen\"><param name=\"android-package\" value=\"org.apache.cordova.splashscreen.SplashScreen\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<feature name=\"NetworkStatus\"><param name=\"android-package\" value=\"org.apache.cordova.networkinformation.NetworkManager\" /></feature>", "count": 1}, {"xml": "<feature name=\"File\"><param name=\"android-package\" value=\"org.apache.cordova.file.FileUtils\" /><param name=\"onload\" value=\"true\" /></feature>", "count": 1}, {"xml": "<allow-navigation href=\"cdvfile:*\" />", "count": 1}]}}, "AndroidManifest.xml": {"parents": {"/*": [{"xml": "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "count": 1}, {"xml": "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "count": 1}]}}}}, "installed_plugins": {"cordova-plugin-device": {"PACKAGE_NAME": "com.karimwahib.passwordgenerator"}, "cordova-plugin-splashscreen": {"PACKAGE_NAME": "com.karimwahib.passwordgenerator"}, "cordova-plugin-network-information": {"PACKAGE_NAME": "com.karimwahib.passwordgenerator"}, "cordova-plugin-file": {"PACKAGE_NAME": "com.karimwahib.passwordgenerator"}}, "dependent_plugins": {}, "modules": [{"id": "cordova-plugin-device.device", "file": "plugins/cordova-plugin-device/www/device.js", "pluginId": "cordova-plugin-device", "clobbers": ["device"]}, {"id": "cordova-plugin-splashscreen.SplashScreen", "file": "plugins/cordova-plugin-splashscreen/www/splashscreen.js", "pluginId": "cordova-plugin-splashscreen", "clobbers": ["navigator.splashscreen"]}, {"id": "cordova-plugin-network-information.network", "file": "plugins/cordova-plugin-network-information/www/network.js", "pluginId": "cordova-plugin-network-information", "clobbers": ["navigator.connection", "navigator.network.connection"]}, {"id": "cordova-plugin-network-information.Connection", "file": "plugins/cordova-plugin-network-information/www/Connection.js", "pluginId": "cordova-plugin-network-information", "clobbers": ["Connection"]}, {"id": "cordova-plugin-file.DirectoryEntry", "file": "plugins/cordova-plugin-file/www/DirectoryEntry.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.DirectoryEntry"]}, {"id": "cordova-plugin-file.DirectoryReader", "file": "plugins/cordova-plugin-file/www/DirectoryReader.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.DirectoryReader"]}, {"id": "cordova-plugin-file.Entry", "file": "plugins/cordova-plugin-file/www/Entry.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.Entry"]}, {"id": "cordova-plugin-file.File", "file": "plugins/cordova-plugin-file/www/File.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.File"]}, {"id": "cordova-plugin-file.FileEntry", "file": "plugins/cordova-plugin-file/www/FileEntry.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileEntry"]}, {"id": "cordova-plugin-file.FileError", "file": "plugins/cordova-plugin-file/www/FileError.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileError"]}, {"id": "cordova-plugin-file.FileReader", "file": "plugins/cordova-plugin-file/www/FileReader.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileReader"]}, {"id": "cordova-plugin-file.FileSystem", "file": "plugins/cordova-plugin-file/www/FileSystem.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileSystem"]}, {"id": "cordova-plugin-file.FileUploadOptions", "file": "plugins/cordova-plugin-file/www/FileUploadOptions.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileUploadOptions"]}, {"id": "cordova-plugin-file.FileUploadResult", "file": "plugins/cordova-plugin-file/www/FileUploadResult.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileUploadResult"]}, {"id": "cordova-plugin-file.FileWriter", "file": "plugins/cordova-plugin-file/www/FileWriter.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.FileWriter"]}, {"id": "cordova-plugin-file.Flags", "file": "plugins/cordova-plugin-file/www/Flags.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.Flags"]}, {"id": "cordova-plugin-file.LocalFileSystem", "file": "plugins/cordova-plugin-file/www/LocalFileSystem.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.LocalFileSystem"], "merges": ["window"]}, {"id": "cordova-plugin-file.Metadata", "file": "plugins/cordova-plugin-file/www/Metadata.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.Metadata"]}, {"id": "cordova-plugin-file.ProgressEvent", "file": "plugins/cordova-plugin-file/www/ProgressEvent.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.ProgressEvent"]}, {"id": "cordova-plugin-file.fileSystems", "file": "plugins/cordova-plugin-file/www/fileSystems.js", "pluginId": "cordova-plugin-file"}, {"id": "cordova-plugin-file.requestFileSystem", "file": "plugins/cordova-plugin-file/www/requestFileSystem.js", "pluginId": "cordova-plugin-file", "clobbers": ["window.requestFileSystem"]}, {"id": "cordova-plugin-file.resolveLocalFileSystemURI", "file": "plugins/cordova-plugin-file/www/resolveLocalFileSystemURI.js", "pluginId": "cordova-plugin-file", "merges": ["window"]}, {"id": "cordova-plugin-file.isChrome", "file": "plugins/cordova-plugin-file/www/browser/isChrome.js", "pluginId": "cordova-plugin-file", "runs": true}, {"id": "cordova-plugin-file.androidFileSystem", "file": "plugins/cordova-plugin-file/www/android/FileSystem.js", "pluginId": "cordova-plugin-file", "merges": ["FileSystem"]}, {"id": "cordova-plugin-file.fileSystems-roots", "file": "plugins/cordova-plugin-file/www/fileSystems-roots.js", "pluginId": "cordova-plugin-file", "runs": true}, {"id": "cordova-plugin-file.fileSystemPaths", "file": "plugins/cordova-plugin-file/www/fileSystemPaths.js", "pluginId": "cordova-plugin-file", "merges": ["<PERSON><PERSON>"], "runs": true}], "plugin_metadata": {"cordova-plugin-device": "2.1.0", "cordova-plugin-splashscreen": "5.0.4", "cordova-plugin-network-information": "2.0.2", "cordova-plugin-file": "6.0.2"}}