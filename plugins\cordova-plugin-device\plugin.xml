<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
    xmlns:rim="http://www.blackberry.com/ns/widgets"
    xmlns:android="http://schemas.android.com/apk/res/android"
    id="cordova-plugin-device"
    version="2.1.0">
    <name>Device</name>
    <description>Cordova Device Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,device</keywords>
    <repo>https://git-wip-us.apache.org/repos/asf/cordova-plugin-device.git</repo>
    <issue>https://issues.apache.org/jira/browse/CB/component/12320648</issue>

    <engines>
        <engine name="cordova-electron" version=">=3.0.0" />
    </engines>

    <js-module src="www/device.js" name="device">
        <clobbers target="device" />
    </js-module>

    <!-- android -->
    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="Device" >
                <param name="android-package" value="org.apache.cordova.device.Device"/>
            </feature>
        </config-file>

        <source-file src="src/android/Device.java" target-dir="src/org/apache/cordova/device" />
    </platform>

    <!-- ios -->
    <platform name="ios">
        <config-file target="config.xml" parent="/*">
            <feature name="Device">
                <param name="ios-package" value="CDVDevice"/>
            </feature>
        </config-file>

        <header-file src="src/ios/CDVDevice.h" />
        <source-file src="src/ios/CDVDevice.m" />
    </platform>

    <!-- electron -->
    <platform name="electron">
        <framework src="src/electron" />
    </platform>

    <!-- windows -->
    <platform name="windows">
        <js-module src="src/windows/DeviceProxy.js" name="DeviceProxy">
            <runs />
        </js-module>
    </platform>

    <!-- browser -->
    <platform name="browser">
        <config-file target="config.xml" parent="/*">
            <feature name="Device">
                <param name="browser-package" value="Device" />
            </feature>
        </config-file>

        <js-module src="src/browser/DeviceProxy.js" name="DeviceProxy">
            <runs />
        </js-module>
    </platform>

    <!-- osx -->
    <platform name="osx">
        <config-file target="config.xml" parent="/*">
            <feature name="Device">
                <param name="ios-package" value="Device"/>
            </feature>
        </config-file>

        <header-file src="src/osx/CDVDevice.h" />
        <source-file src="src/osx/CDVDevice.m" />
    </platform>


</plugin>
