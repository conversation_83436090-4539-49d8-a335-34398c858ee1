<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.karimwahib.passwordgenerator"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <permission
        android:name="com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@android:drawable/ic_lock_idle_lock"
        android:label="@string/app_name"
        android:roundIcon="@android:drawable/ic_lock_idle_lock"
        android:supportsRtl="true"
        android:theme="@style/Theme.PasswordGeneratorPro"
        android:usesCleartextTraffic="true" >

        <!-- دعم الشاشات المختلفة -->
        <meta-data
            android:name="android.webkit.WebView.MetricsOptOut"
            android:value="true" />
        <meta-data
            android:name="android.webkit.WebView.EnableSafeBrowsing"
            android:value="false" />

        <activity
            android:name="com.karimwahib.passwordgenerator.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.PasswordGeneratorPro.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Splash Screen Activity -->
        <activity
            android:name="com.karimwahib.passwordgenerator.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme" >
        </activity>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.karimwahib.passwordgenerator.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>