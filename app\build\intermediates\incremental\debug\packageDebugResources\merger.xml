<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\my project program\update\password genretor\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\my project program\update\password genretor\app\src\main\res"><file name="gradient_background" path="E:\my project program\update\password genretor\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="ic_app_logo" path="E:\my project program\update\password genretor\app\src\main\res\drawable\ic_app_logo.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\my project program\update\password genretor\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\my project program\update\password genretor\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_simple" path="E:\my project program\update\password genretor\app\src\main\res\drawable\ic_launcher_simple.xml" qualifiers="" type="drawable"/><file name="splash_background" path="E:\my project program\update\password genretor\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\my project program\update\password genretor\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_splash" path="E:\my project program\update\password genretor\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\my project program\update\password genretor\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\my project program\update\password genretor\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="E:\my project program\update\password genretor\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_color">#667eea</color><color name="primary_dark">#5a6fd8</color><color name="secondary_color">#764ba2</color><color name="accent_color">#667eea</color><color name="background_start">#667eea</color><color name="background_end">#764ba2</color><color name="surface_color">#FFFFFF</color><color name="text_primary">#FFFFFF</color><color name="text_secondary">#E0E0E0</color><color name="text_hint">#BDBDBD</color><color name="success_color">#4CAF50</color><color name="warning_color">#FF9800</color><color name="error_color">#F44336</color><color name="overlay_light">#33FFFFFF</color><color name="overlay_dark">#33000000</color><color name="status_bar_color">#5a6fd8</color><color name="navigation_bar_color">#667eea</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="E:\my project program\update\password genretor\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Password Generator Pro</string><string name="app_subtitle">مولد كلمات مرور قوية</string><string name="app_logo">شعار التطبيق</string><string name="developer_info">Developed by Karim Wahib</string><string name="password_copied">تم نسخ كلمة المرور!</string><string name="password_saved">تم حفظ كلمة المرور!</string><string name="error_message">حدث خطأ، يرجى المحاولة مرة أخرى</string><string name="copy_password">نسخ كلمة المرور</string><string name="generate_password">إنشاء كلمة مرور</string><string name="save_password">حفظ كلمة المرور</string><string name="version_name">الإصدار 1.0</string><string name="copyright">© 2024 Karim Wahib. جميع الحقوق محفوظة.</string></file><file path="E:\my project program\update\password genretor\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.PasswordGeneratorPro" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/secondary_color</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <item name="android:navigationBarColor">@color/navigation_bar_color</item>
        
        <item name="android:windowBackground">@drawable/gradient_background</item>
    </style><style name="Theme.PasswordGeneratorPro.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="SplashTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">@color/primary_color</item>
        <item name="android:navigationBarColor">@color/primary_color</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
    </style><style name="AppButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary_color</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="AppTextTitle">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textStyle">bold</item>
    </style><style name="AppTextSubtitle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="AppTextBody">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style></file><file name="backup_rules" path="E:\my project program\update\password genretor\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\my project program\update\password genretor\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\my project program\update\password genretor\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\my project program\update\password genretor\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\my project program\update\password genretor\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\my project program\update\password genretor\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>