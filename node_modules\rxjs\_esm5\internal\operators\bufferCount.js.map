{"version": 3, "file": "bufferCount.js", "sources": ["../../../src/internal/operators/bufferCount.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AA2D3C,MAAM,UAAU,WAAW,CAAI,UAAkB,EAAE,gBAA+B;IAA/B,iCAAA,EAAA,uBAA+B;IAChF,OAAO,SAAS,2BAA2B,CAAC,MAAqB;QAC/D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAI,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;AACJ,CAAC;AAED;IAGE,6BAAoB,UAAkB,EAAU,gBAAwB;QAApD,eAAU,GAAV,UAAU,CAAQ;QAAU,qBAAgB,GAAhB,gBAAgB,CAAQ;QACtE,IAAI,CAAC,gBAAgB,IAAI,UAAU,KAAK,gBAAgB,EAAE;YACxD,IAAI,CAAC,eAAe,GAAG,qBAAqB,CAAC;SAC9C;aAAM;YACL,IAAI,CAAC,eAAe,GAAG,yBAAyB,CAAC;SAClD;IACH,CAAC;IAED,kCAAI,GAAJ,UAAK,UAA2B,EAAE,MAAW;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACxG,CAAC;IACH,0BAAC;AAAD,CAAC,AAdD,IAcC;AAOD;IAAuC,iDAAa;IAGlD,+BAAY,WAA4B,EAAU,UAAkB;QAApE,YACE,kBAAM,WAAW,CAAC,SACnB;QAFiD,gBAAU,GAAV,UAAU,CAAQ;QAF5D,YAAM,GAAQ,EAAE,CAAC;;IAIzB,CAAC;IAES,qCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEnB,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;SAClB;IACH,CAAC;IAES,yCAAS,GAAnB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/B;QACD,iBAAM,SAAS,WAAE,CAAC;IACpB,CAAC;IACH,4BAAC;AAAD,CAAC,AAzBD,CAAuC,UAAU,GAyBhD;AAOD;IAA2C,qDAAa;IAItD,mCAAY,WAA4B,EAAU,UAAkB,EAAU,gBAAwB;QAAtG,YACE,kBAAM,WAAW,CAAC,SACnB;QAFiD,gBAAU,GAAV,UAAU,CAAQ;QAAU,sBAAgB,GAAhB,gBAAgB,CAAQ;QAH9F,aAAO,GAAe,EAAE,CAAC;QACzB,WAAK,GAAW,CAAC,CAAC;;IAI1B,CAAC;IAES,yCAAK,GAAf,UAAgB,KAAQ;QAChB,IAAA,SAAuD,EAArD,0BAAU,EAAE,sCAAgB,EAAE,oBAAO,EAAE,gBAAK,CAAU;QAE9D,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,KAAK,GAAG,gBAAgB,KAAK,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAClB;QAED,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAI;YAClC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;gBAChC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC/B;SACF;IACH,CAAC;IAES,6CAAS,GAAnB;QACQ,IAAA,SAA+B,EAA7B,oBAAO,EAAE,4BAAW,CAAU;QAEtC,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAI,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC1B;SACF;QACD,iBAAM,SAAS,WAAE,CAAC;IACpB,CAAC;IAEH,gCAAC;AAAD,CAAC,AAtCD,CAA2C,UAAU,GAsCpD"}