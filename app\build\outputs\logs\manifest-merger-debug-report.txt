-- Merging decision tree log ---
manifest
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:2:1-41:12
MERGED from [androidx.databinding:viewbinding:8.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60ec740f90babd637eadf6c6528c7d8\transformed\viewbinding-8.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\829c87f4619f604d1b4a8264ea88cd0b\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c20b2a679504b03839031702dc2cbe9a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fbac7b9eeb13472eea558bcd4d59c88\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6bee3e3ef0e8dd78137af179678f060\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c4f3bcde07cb52b2e31f73683065188\transformed\webkit-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d275d724d45b9caa2bd2468e64778e87\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c421ba69369bc596900c11244faaa12\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\342148fd63d0b74470e7d69af6e75129\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbe8614aaa0c36894e0d1b64d282ca3c\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8ec014f8204fb4da13c4eaa24abf0d4b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb56e4575aa34156784ec154624e9f5c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\455a9ee24753308910a828e5709d58fb\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\673451a9d4eabfa755722c4a50ca09b7\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e26423365a740354ae4ca2b232e5e1c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3734d07cd914cf93f7770c5bd61c1747\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ad06749662db120ffc9fd62a3d9c07a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eadeb196a99da1d02ef6d5ae1623817\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ff8e6498c484f4356039bf8dac4ace2\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2594f5b7277b5c93bd1f31795e0a5c08\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c3d2af0c4b14dee2515f4b3f235231e\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e5d76635728dfacb88410ec9a50d8e6\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c87d24063170126f3379e7d9e954e6dd\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1464df91dc9ff31b05eb1c454124c26\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0bad4ffe67c6c45b67c04ee986546f8\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8aeed0c41eeb71e78065b370fd3229d6\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f03777f77681bbf46db89ba2d457ec3\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8754311f299dd32058e488676d1ac5\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\398e5768f768a4e68c38d93609c68d2d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\50d948d6c1eaf98ceaf025fc964fd0dd\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c30c430914e360b909a33b9219c4e81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1c1b20b9cb6ee160fc11fc6e6db79d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19ce4d9d6b275820d02a4b6f980d3230\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\448d63a12f7fb24244ef2328934c2f8c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e56bc57cc3fb28926b25b9124c19a046\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e41419e7e037306e183b84330e747a5\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a03721f4a20991e7d0459424cffa0c9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c2d58d4fba9fdc411d4bfac4773790d6\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3dccdb27f017ec003f020532fe15a178\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.VIBRATE
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:6:5-66
	android:name
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:6:22-63
application
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:8:5-39:19
INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:8:5-39:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\829c87f4619f604d1b4a8264ea88cd0b\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\829c87f4619f604d1b4a8264ea88cd0b\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c20b2a679504b03839031702dc2cbe9a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c20b2a679504b03839031702dc2cbe9a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c30c430914e360b909a33b9219c4e81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c30c430914e360b909a33b9219c4e81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1c1b20b9cb6ee160fc11fc6e6db79d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1c1b20b9cb6ee160fc11fc6e6db79d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:15:9-35
	android:label
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:13:9-41
	android:fullBackupContent
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:11:9-54
	android:roundIcon
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:14:9-57
	tools:targetApi
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:18:9-29
	android:icon
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:12:9-52
	android:allowBackup
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:9:9-35
	android:theme
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:16:9-58
	android:dataExtractionRules
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:10:9-65
	android:usesCleartextTraffic
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:17:9-44
activity#com.karimwahib.passwordgenerator.MainActivity
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:20:9-29:20
	android:screenOrientation
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:23:13-49
	android:exported
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:24:13-74
	android:name
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:25:13-28:29
action#android.intent.action.MAIN
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:26:17-69
	android:name
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:26:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:27:17-77
	android:name
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:27:27-74
activity#com.karimwahib.passwordgenerator.SplashActivity
ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:32:9-37:20
	android:screenOrientation
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:35:13-49
	android:exported
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:34:13-36
	android:theme
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:36:13-47
	android:name
		ADDED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:33:13-43
uses-sdk
INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60ec740f90babd637eadf6c6528c7d8\transformed\viewbinding-8.1.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60ec740f90babd637eadf6c6528c7d8\transformed\viewbinding-8.1.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\829c87f4619f604d1b4a8264ea88cd0b\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\829c87f4619f604d1b4a8264ea88cd0b\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c20b2a679504b03839031702dc2cbe9a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c20b2a679504b03839031702dc2cbe9a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fbac7b9eeb13472eea558bcd4d59c88\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6fbac7b9eeb13472eea558bcd4d59c88\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6bee3e3ef0e8dd78137af179678f060\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6bee3e3ef0e8dd78137af179678f060\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c4f3bcde07cb52b2e31f73683065188\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c4f3bcde07cb52b2e31f73683065188\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d275d724d45b9caa2bd2468e64778e87\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d275d724d45b9caa2bd2468e64778e87\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c421ba69369bc596900c11244faaa12\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c421ba69369bc596900c11244faaa12\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\342148fd63d0b74470e7d69af6e75129\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\342148fd63d0b74470e7d69af6e75129\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbe8614aaa0c36894e0d1b64d282ca3c\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbe8614aaa0c36894e0d1b64d282ca3c\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8ec014f8204fb4da13c4eaa24abf0d4b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8ec014f8204fb4da13c4eaa24abf0d4b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb56e4575aa34156784ec154624e9f5c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb56e4575aa34156784ec154624e9f5c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\455a9ee24753308910a828e5709d58fb\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\455a9ee24753308910a828e5709d58fb\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\673451a9d4eabfa755722c4a50ca09b7\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\673451a9d4eabfa755722c4a50ca09b7\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e26423365a740354ae4ca2b232e5e1c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e26423365a740354ae4ca2b232e5e1c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3734d07cd914cf93f7770c5bd61c1747\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3734d07cd914cf93f7770c5bd61c1747\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ad06749662db120ffc9fd62a3d9c07a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ad06749662db120ffc9fd62a3d9c07a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eadeb196a99da1d02ef6d5ae1623817\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eadeb196a99da1d02ef6d5ae1623817\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ff8e6498c484f4356039bf8dac4ace2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ff8e6498c484f4356039bf8dac4ace2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2594f5b7277b5c93bd1f31795e0a5c08\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2594f5b7277b5c93bd1f31795e0a5c08\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c3d2af0c4b14dee2515f4b3f235231e\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c3d2af0c4b14dee2515f4b3f235231e\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e5d76635728dfacb88410ec9a50d8e6\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e5d76635728dfacb88410ec9a50d8e6\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c87d24063170126f3379e7d9e954e6dd\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c87d24063170126f3379e7d9e954e6dd\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1464df91dc9ff31b05eb1c454124c26\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e1464df91dc9ff31b05eb1c454124c26\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0bad4ffe67c6c45b67c04ee986546f8\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0bad4ffe67c6c45b67c04ee986546f8\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8aeed0c41eeb71e78065b370fd3229d6\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8aeed0c41eeb71e78065b370fd3229d6\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f03777f77681bbf46db89ba2d457ec3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f03777f77681bbf46db89ba2d457ec3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8754311f299dd32058e488676d1ac5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8754311f299dd32058e488676d1ac5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\398e5768f768a4e68c38d93609c68d2d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\398e5768f768a4e68c38d93609c68d2d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\50d948d6c1eaf98ceaf025fc964fd0dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\50d948d6c1eaf98ceaf025fc964fd0dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c30c430914e360b909a33b9219c4e81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c30c430914e360b909a33b9219c4e81\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1c1b20b9cb6ee160fc11fc6e6db79d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1c1b20b9cb6ee160fc11fc6e6db79d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19ce4d9d6b275820d02a4b6f980d3230\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19ce4d9d6b275820d02a4b6f980d3230\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\448d63a12f7fb24244ef2328934c2f8c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\448d63a12f7fb24244ef2328934c2f8c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e56bc57cc3fb28926b25b9124c19a046\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e56bc57cc3fb28926b25b9124c19a046\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e41419e7e037306e183b84330e747a5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e41419e7e037306e183b84330e747a5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a03721f4a20991e7d0459424cffa0c9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a03721f4a20991e7d0459424cffa0c9\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c2d58d4fba9fdc411d4bfac4773790d6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c2d58d4fba9fdc411d4bfac4773790d6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3dccdb27f017ec003f020532fe15a178\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3dccdb27f017ec003f020532fe15a178\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1c1b20b9cb6ee160fc11fc6e6db79d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d1c1b20b9cb6ee160fc11fc6e6db79d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
