<?xml version='1.0' encoding='utf-8'?>
<widget id="com.karimwahib.passwordgenerator" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <feature name="Device">
        <param name="android-package" value="org.apache.cordova.device.Device" />
    </feature>
    <feature name="SplashScreen">
        <param name="android-package" value="org.apache.cordova.splashscreen.SplashScreen" />
        <param name="onload" value="true" />
    </feature>
    <feature name="NetworkStatus">
        <param name="android-package" value="org.apache.cordova.networkinformation.NetworkManager" />
    </feature>
    <feature name="File">
        <param name="android-package" value="org.apache.cordova.file.FileUtils" />
        <param name="onload" value="true" />
    </feature>
    <allow-navigation href="cdvfile:*" />
    <name>Password Generator Pro</name>
    <description>
        مولد كلمات مرور قوية مع محفظة آمنة لحفظ كلمات المرور. إنشاء كلمات مرور معقدة وآمنة مع إمكانية حفظها وإدارتها بسهولة.
    </description>
    <author email="<EMAIL>" href="https://wa.me/201159296333">
        Karim Wahib
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <allow-navigation href="*" />
    <allow-intent href="market:*" />
    <icon density="ldpi" src="res/icons/android/ldpi.png" />
    <icon density="mdpi" src="res/icons/android/mdpi.png" />
    <icon density="hdpi" src="res/icons/android/hdpi.png" />
    <icon density="xhdpi" src="res/icons/android/xhdpi.png" />
    <icon density="xxhdpi" src="res/icons/android/xxhdpi.png" />
    <icon density="xxxhdpi" src="res/icons/android/xxxhdpi.png" />
    <splash density="land-ldpi" src="res/screens/android/land-ldpi.png" />
    <splash density="land-mdpi" src="res/screens/android/land-mdpi.png" />
    <splash density="land-hdpi" src="res/screens/android/land-hdpi.png" />
    <splash density="land-xhdpi" src="res/screens/android/land-xhdpi.png" />
    <splash density="land-xxhdpi" src="res/screens/android/land-xxhdpi.png" />
    <splash density="land-xxxhdpi" src="res/screens/android/land-xxxhdpi.png" />
    <splash density="port-ldpi" src="res/screens/android/port-ldpi.png" />
    <splash density="port-mdpi" src="res/screens/android/port-mdpi.png" />
    <splash density="port-hdpi" src="res/screens/android/port-hdpi.png" />
    <splash density="port-xhdpi" src="res/screens/android/port-xhdpi.png" />
    <splash density="port-xxhdpi" src="res/screens/android/port-xxhdpi.png" />
    <splash density="port-xxxhdpi" src="res/screens/android/port-xxxhdpi.png" />
    <preference name="loglevel" value="DEBUG" />
    <preference name="Orientation" value="portrait" />
    <preference name="Fullscreen" value="false" />
    <preference name="StatusBarOverlaysWebView" value="false" />
    <preference name="StatusBarBackgroundColor" value="#667eea" />
    <preference name="StatusBarStyle" value="lightcontent" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="AutoHideSplashScreen" value="true" />
    <preference name="FadeSplashScreen" value="true" />
    <preference name="FadeSplashScreenDuration" value="750" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    <preference name="DisallowOverscroll" value="true" />
    <preference name="BackgroundColor" value="0xff667eea" />
    <preference name="AllowInlineMediaPlayback" value="false" />
    <preference name="BackupWebStorage" value="local" />
    <preference name="TopActivityIndicator" value="gray" />
    <preference name="KeyboardDisplayRequiresUserAction" value="true" />
    <preference name="SuppressesIncrementalRendering" value="false" />
    <preference name="GapBetweenPages" value="0" />
    <preference name="PageLength" value="0" />
    <preference name="PaginationBreakingMode" value="page" />
    <preference name="PaginationMode" value="unpaginated" />
    <preference name="android-minSdkVersion" value="22" />
    <preference name="android-targetSdkVersion" value="33" />
    <preference name="android-compileSdkVersion" value="33" />
</widget>
