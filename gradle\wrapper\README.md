# Gradle Wrapper

هذا المجلد يحتوي على ملفات Gradle Wrapper المطلوبة.

## الملفات المطلوبة:

1. **gradle-wrapper.properties** ✅ (موجود)
2. **gradle-wrapper.jar** ❌ (سيتم تحميله تلقائياً)

## كيفية الحصول على gradle-wrapper.jar:

### الطريقة الأولى: Android Studio (الأسهل)
1. افتح المشروع في Android Studio
2. سيقوم Android Studio بتحميل الملف تلقائياً
3. انتظر حتى ينتهي Gradle Sync

### الطريقة الثانية: سطر الأوامر
```bash
# في مجلد المشروع
gradle wrapper
```

### الطريقة الثالثة: تحميل يدوي
1. اذهب إلى: https://gradle.org/releases/
2. حمل Gradle 8.0
3. استخرج الملف gradle-wrapper.jar من المجلد
4. ضعه في gradle/wrapper/

## ملاحظة مهمة:
لا تقلق! عند فتح المشروع في Android Studio لأول مرة، سيتم تحميل جميع الملفات المطلوبة تلقائياً.

فقط افتح المشروع واتركه يعمل! 🚀
