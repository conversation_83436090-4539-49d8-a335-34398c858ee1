<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_background"
    android:fitsSystemWindows="true"
    tools:context=".SplashActivity">

    <!-- شعار التطبيق -->
    <ImageView
        android:id="@+id/logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="24dp"
        android:src="@drawable/ic_app_logo"
        android:contentDescription="@string/app_logo"
        app:layout_constraintBottom_toTopOf="@+id/app_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- اسم التطبيق -->
    <TextView
        android:id="@+id/app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/app_name"
        android:textColor="@android:color/white"
        android:textSize="28sp"
        android:textStyle="bold"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintBottom_toTopOf="@+id/app_subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/logo" />

    <!-- وصف التطبيق -->
    <TextView
        android:id="@+id/app_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="48dp"
        android:text="@string/app_subtitle"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:alpha="0.9"
        android:fontFamily="sans-serif"
        app:layout_constraintBottom_toTopOf="@+id/developer_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/app_name" />

    <!-- معلومات المطور -->
    <TextView
        android:id="@+id/developer_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="@string/developer_info"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:alpha="0.7"
        android:fontFamily="sans-serif-light"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- شريط التحميل -->
    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="200dp"
        android:layout_height="4dp"
        android:layout_marginTop="16dp"
        android:indeterminate="true"
        android:progressTint="@android:color/white"
        android:alpha="0.8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/developer_info" />

</androidx.constraintlayout.widget.ConstraintLayout>
