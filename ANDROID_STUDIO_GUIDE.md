# دليل Android Studio - Password Generator Pro

## 🚀 فتح المشروع في Android Studio

### الخطوة 1: فتح المشروع
1. افتح Android Studio
2. اختر "Open an Existing Project"
3. انتقل إلى مجلد المشروع واختر المجلد الجذر
4. انتظر حتى يتم تحميل المشروع وفهرسة الملفات

### الخطوة 2: التحقق من الإعدادات
1. **Gradle Sync**: سيتم تلقائياً، إذا لم يحدث اضغط "Sync Now"
2. **SDK Version**: تأكد من تثبيت Android SDK 34
3. **Build Tools**: تأكد من تثبيت أحدث إصدار

## 🔧 بناء التطبيق

### للتطوير والاختبار
```bash
# في Terminal داخل Android Studio
./gradlew assembleDebug
```

أو استخدم الواجهة:
- **Build** → **Make Project** (Ctrl+F9)
- **Build** → **Build Bundle(s) / APK(s)** → **Build APK(s)**

### للإنتاج
```bash
./gradlew assembleRelease
```

أو:
- **Build** → **Generate Signed Bundle / APK**
- اختر **APK**
- أنشئ أو اختر keystore
- املأ معلومات التوقيع

## 📱 تشغيل التطبيق

### على المحاكي
1. **Tools** → **AVD Manager**
2. أنشئ جهاز افتراضي جديد أو استخدم موجود
3. اضغط **Run** (Shift+F10)

### على الجهاز الحقيقي
1. فعّل **Developer Options** في الأندرويد
2. فعّل **USB Debugging**
3. وصّل الجهاز بالكمبيوتر
4. اضغط **Run** (Shift+F10)

## 📁 هيكل المشروع

```
Password Generator Pro/
├── app/
│   ├── build.gradle                 # إعدادات التطبيق
│   ├── proguard-rules.pro          # قواعد ProGuard
│   └── src/
│       └── main/
│           ├── AndroidManifest.xml  # إعدادات التطبيق
│           ├── java/com/karimwahib/passwordgenerator/
│           │   ├── MainActivity.java      # النشاط الرئيسي
│           │   └── SplashActivity.java    # شاشة البداية
│           ├── res/
│           │   ├── drawable/        # الصور والأيقونات
│           │   ├── layout/          # تخطيطات الواجهة
│           │   ├── values/          # الألوان والنصوص
│           │   └── xml/             # ملفات XML إضافية
│           └── assets/
│               └── index.html       # ملف التطبيق الرئيسي
├── build.gradle                     # إعدادات المشروع
├── gradle.properties               # خصائص Gradle
└── settings.gradle                 # إعدادات المشروع
```

## 🎨 تخصيص التطبيق

### تغيير الألوان
عدّل ملف `app/src/main/res/values/colors.xml`:
```xml
<color name="primary_color">#YOUR_COLOR</color>
<color name="background_start">#YOUR_START_COLOR</color>
<color name="background_end">#YOUR_END_COLOR</color>
```

### تغيير النصوص
عدّل ملف `app/src/main/res/values/strings.xml`:
```xml
<string name="app_name">اسم التطبيق الجديد</string>
<string name="app_subtitle">وصف التطبيق</string>
```

### تغيير الأيقونة
1. استبدل الملفات في `app/src/main/res/mipmap/`
2. أو استخدم **File** → **New** → **Image Asset**

### تغيير معرف التطبيق
عدّل في `app/build.gradle`:
```gradle
defaultConfig {
    applicationId "com.yourname.yourapp"
    // ...
}
```

## 🔐 إنشاء APK موقع للنشر

### الطريقة الأولى: من الواجهة
1. **Build** → **Generate Signed Bundle / APK**
2. اختر **APK**
3. **Create new keystore** أو اختر موجود
4. املأ المعلومات:
   - **Keystore path**: مسار حفظ المفتاح
   - **Password**: كلمة مرور قوية
   - **Key alias**: اسم المفتاح
   - **Key password**: كلمة مرور المفتاح
   - **Validity**: 25 سنة
5. اختر **release** build variant
6. اضغط **Finish**

### الطريقة الثانية: من Terminal
```bash
# إنشاء keystore
keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000

# بناء APK موقع
./gradlew assembleRelease
```

## 🧪 اختبار التطبيق

### اختبارات أساسية
- [ ] فتح التطبيق بنجاح
- [ ] عمل جميع وظائف مولد كلمات المرور
- [ ] عمل المحفظة وحفظ البيانات
- [ ] تبديل اللغة بين العربية والإنجليزية
- [ ] نسخ كلمات المرور للحافظة
- [ ] فتح رابط الواتساب

### اختبار على أجهزة مختلفة
- هواتف بأحجام شاشة مختلفة
- إصدارات أندرويد مختلفة (API 21+)
- اختبار الأداء والسرعة

## 🚀 نشر على Google Play

### تحضير الملفات
1. **APK موقع**: من `app/build/outputs/apk/release/`
2. **أيقونة عالية الجودة**: 512x512 px
3. **لقطات شاشة**: 8 صور على الأقل
4. **وصف التطبيق**: استخدم `STORE_DESCRIPTION.md`
5. **سياسة الخصوصية**: استخدم `PRIVACY_POLICY.md`

### خطوات النشر
1. اذهب إلى [Google Play Console](https://play.google.com/console)
2. أنشئ تطبيق جديد
3. ارفع APK في قسم "App releases"
4. املأ معلومات المتجر
5. حدد السعر والتوزيع
6. أرسل للمراجعة

## 🛠️ نصائح مهمة

### الأمان
- احتفظ بنسخة آمنة من keystore
- لا تشارك كلمات مرور المفاتيح
- استخدم كلمات مرور قوية

### الأداء
- اختبر على أجهزة ضعيفة
- راقب استهلاك الذاكرة
- تأكد من سرعة التحميل

### الجودة
- اختبر جميع الوظائف
- تأكد من وضوح النصوص
- راجع التصميم على شاشات مختلفة

## 🆘 حل المشاكل الشائعة

### مشكلة Gradle Sync
```bash
# في Terminal
./gradlew clean
./gradlew build
```

### مشكلة SDK
- **File** → **Project Structure** → **SDK Location**
- تأكد من تثبيت Android SDK المطلوب

### مشكلة Build
- تأكد من اتصال الإنترنت
- امسح cache: **File** → **Invalidate Caches and Restart**

## 📞 الدعم

إذا واجهت أي مشاكل:
- **واتساب**: [+20 115 929 6333](https://wa.me/201159296333)
- **البريد الإلكتروني**: <EMAIL>

---

**مبروك! مشروعك جاهز للبناء في Android Studio! 🎉**
