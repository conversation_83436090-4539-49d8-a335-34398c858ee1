<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
           id="cordova-plugin-whitelist"
      version="1.3.5">
    <name>Whitelist</name>
    <description>Cordova Network Whitelist Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,whitelist,policy</keywords>

    <engines>
      <engine name="cordova-android" version=">=4.0.0 <10.0.0" />
    </engines>

    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="Whitelist" >
                <param name="android-package" value="org.apache.cordova.whitelist.WhitelistPlugin"/>
                <param name="onload" value="true" />
            </feature>
        </config-file>

        <source-file src="src/android/WhitelistPlugin.java" target-dir="src/org/apache/cordova/whitelist" />
    </platform>

</plugin>
