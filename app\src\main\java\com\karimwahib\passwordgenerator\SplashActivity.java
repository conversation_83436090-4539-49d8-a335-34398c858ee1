package com.karimwahib.passwordgenerator;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;

public class SplashActivity extends AppCompatActivity {

    private static final int SPLASH_DURATION = 2000; // 2 ثانية

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // إخفاء شريط الحالة للحصول على تجربة ملء الشاشة
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        // تأخير لمدة ثانيتين ثم الانتقال للنشاط الرئيسي
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            Intent intent = new Intent(SplashActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
            
            // تأثير انتقال سلس
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
        }, SPLASH_DURATION);
    }

    @Override
    public void onBackPressed() {
        // منع المستخدم من الرجوع أثناء شاشة البداية
        // لا نفعل شيء
    }
}
