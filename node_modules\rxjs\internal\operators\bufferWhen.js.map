{"version": 3, "file": "bufferWhen.js", "sources": ["../../src/internal/operators/bufferWhen.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA,gDAA+C;AAE/C,oDAAiG;AA4CjG,SAAgB,UAAU,CAAI,eAAsC;IAClE,OAAO,UAAU,MAAqB;QACpC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC;AACJ,CAAC;AAJD,gCAIC;AAED;IAEE,4BAAoB,eAAsC;QAAtC,oBAAe,GAAf,eAAe,CAAuB;IAC1D,CAAC;IAED,iCAAI,GAAJ,UAAK,UAA2B,EAAE,MAAW;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACtF,CAAC;IACH,yBAAC;AAAD,CAAC,AARD,IAQC;AAOD;IAAsC,wCAA6B;IAKjE,8BAAY,WAA4B,EAAU,eAAsC;QAAxF,YACE,kBAAM,WAAW,CAAC,SAEnB;QAHiD,qBAAe,GAAf,eAAe,CAAuB;QAHhF,iBAAW,GAAY,KAAK,CAAC;QAKnC,KAAI,CAAC,UAAU,EAAE,CAAC;;IACpB,CAAC;IAES,oCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAES,wCAAS,GAAnB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;SAChC;QACD,iBAAM,SAAS,WAAE,CAAC;IACpB,CAAC;IAGD,2CAAY,GAAZ;QACE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,yCAAU,GAAV;QACE,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,6CAAc,GAAd;QACE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjB;aAAM;YACL,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;IACH,CAAC;IAED,yCAAU,GAAV;QACQ,IAAA,8CAAmB,CAAU;QAEnC,IAAI,mBAAmB,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YACjC,mBAAmB,CAAC,WAAW,EAAE,CAAC;SACnC;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,eAAe,CAAC;QACpB,IAAI;YACM,IAAA,sCAAe,CAAU;YACjC,eAAe,GAAG,eAAe,EAAE,CAAC;SACrC;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxB;QACD,mBAAmB,GAAG,IAAI,2BAAY,EAAE,CAAC;QACzC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,mBAAmB,CAAC,GAAG,CAAC,+BAAc,CAAC,eAAe,EAAE,IAAI,sCAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1F,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IACH,2BAAC;AAAD,CAAC,AArED,CAAsC,sCAAqB,GAqE1D"}