{"version": 3, "file": "expand.js", "sources": ["../../../src/internal/operators/expand.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AA2DjG,MAAM,UAAU,MAAM,CAAO,OAAwD,EACxD,aAAqB,MAAM,CAAC,iBAAiB,EAC7C,SAAyB;IACpD,UAAU,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC;IAE3E,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;AACpG,CAAC;AAED,MAAM,OAAO,cAAc;IACzB,YAAoB,OAAwD,EACxD,UAAkB,EAClB,SAAyB;QAFzB,YAAO,GAAP,OAAO,CAAiD;QACxD,eAAU,GAAV,UAAU,CAAQ;QAClB,cAAS,GAAT,SAAS,CAAgB;IAC7C,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3G,CAAC;CACF;AAcD,MAAM,OAAO,gBAAuB,SAAQ,qBAA2B;IAMrE,YAAY,WAA0B,EAClB,OAAwD,EACxD,UAAkB,EAClB,SAAyB;QAC3C,KAAK,CAAC,WAAW,CAAC,CAAC;QAHD,YAAO,GAAP,OAAO,CAAiD;QACxD,eAAU,GAAV,UAAU,CAAQ;QAClB,cAAS,GAAT,SAAS,CAAgB;QARrC,UAAK,GAAW,CAAC,CAAC;QAClB,WAAM,GAAW,CAAC,CAAC;QACnB,iBAAY,GAAY,KAAK,CAAC;QAQpC,IAAI,UAAU,GAAG,MAAM,CAAC,iBAAiB,EAAE;YACzC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;SAClB;IACH,CAAC;IAEO,MAAM,CAAC,QAAQ,CAAO,GAAsB;QAClD,MAAM,EAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAC,GAAG,GAAG,CAAC;QAC/C,UAAU,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAES,KAAK,CAAC,KAAU;QACxB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAErC,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;SACR;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACjC,WAAW,CAAC,IAAK,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBACzB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnB,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBAClD;qBAAM;oBACL,MAAM,KAAK,GAAsB,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;oBAC5E,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;oBACrD,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAoB,gBAAgB,CAAC,QAAe,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;iBACzG;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,WAAW,CAAC,KAAM,CAAC,CAAC,CAAC,CAAC;aACvB;SACF;aAAM;YACL,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;IACH,CAAC;IAEO,qBAAqB,CAAC,MAAW,EAAE,KAAQ,EAAE,KAAa;QAChE,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IAES,SAAS;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,UAAU,CAAC,UAAa;QACtB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACzB,CAAC;IAED,cAAc;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;SAC5B;QACD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;IACH,CAAC;CACF"}