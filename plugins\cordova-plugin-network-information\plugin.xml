<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
xmlns:android="http://schemas.android.com/apk/res/android"
           id="cordova-plugin-network-information"
      version="2.0.2">

    <name>Network Information</name>
    <description>Cordova Network Information Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,network,information</keywords>
    <repo>https://github.com/apache/cordova-plugin-network-information</repo>
    <issue>https://github.com/apache/cordova-plugin-network-information/issues</issue>

    <js-module src="www/network.js" name="network">
        <clobbers target="navigator.connection" />
        <clobbers target="navigator.network.connection" />
    </js-module>

    <js-module src="www/Connection.js" name="Connection">
        <clobbers target="Connection" />
    </js-module>


    <!-- android -->
    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="NetworkStatus">
                <param name="android-package" value="org.apache.cordova.networkinformation.NetworkManager"/>
            </feature>
        </config-file>

        <config-file target="AndroidManifest.xml" parent="/*">
            <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
        </config-file>

        <source-file src="src/android/NetworkManager.java" target-dir="src/org/apache/cordova/networkinformation" />

    </platform>

    <!-- ios -->
    <platform name="ios">
        <config-file target="config.xml" parent="/*">
            <feature name="NetworkStatus">
                <param name="ios-package" value="CDVConnection" />
            </feature>
        </config-file>

        <header-file src="src/ios/CDVConnection.h" />
        <source-file src="src/ios/CDVConnection.m" />
        <header-file src="src/ios/CDVReachability.h" />
	    <source-file src="src/ios/CDVReachability.m" />
	    <framework src="SystemConfiguration.framework" weak="true" />
	    <framework src="CoreTelephony.framework" />
    </platform>

    <!-- windows -->
    <platform name="windows">
        <js-module src="src/windows/NetworkInfoProxy.js" name="NetworkInfoProxy">
            <runs/>
        </js-module>
    </platform>

    <!-- browser -->
    <platform name="browser">
        <js-module src="src/browser/network.js" name="NetworkInfoProxy">
            <runs/>
        </js-module>
    </platform>
</plugin>
