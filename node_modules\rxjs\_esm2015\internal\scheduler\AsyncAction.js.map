{"version": 3, "file": "AsyncAction.js", "sources": ["../../../src/internal/scheduler/AsyncAction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAUlC,MAAM,OAAO,WAAe,SAAQ,MAAS;IAO3C,YAAsB,SAAyB,EACzB,IAAmD;QACvE,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAFH,cAAS,GAAT,SAAS,CAAgB;QACzB,SAAI,GAAJ,IAAI,CAA+C;QAH/D,YAAO,GAAY,KAAK,CAAC;IAKnC,CAAC;IAEM,QAAQ,CAAC,KAAS,EAAE,QAAgB,CAAC;QAE1C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QAGD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAuBjC,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;SACrD;QAID,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAEpE,OAAO,IAAI,CAAC;IACd,CAAC;IAES,cAAc,CAAC,SAAyB,EAAE,EAAQ,EAAE,QAAgB,CAAC;QAC7E,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAES,cAAc,CAAC,SAAyB,EAAE,EAAO,EAAE,QAAgB,CAAC;QAE5E,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACpE,OAAO,EAAE,CAAC;SACX;QAGD,aAAa,CAAC,EAAE,CAAC,CAAC;QAClB,OAAO,SAAS,CAAC;IACnB,CAAC;IAMM,OAAO,CAAC,KAAQ,EAAE,KAAa;QAEpC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACT,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE;YAcpD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SAC9D;IACH,CAAC;IAES,QAAQ,CAAC,KAAQ,EAAE,KAAa;QACxC,IAAI,OAAO,GAAY,KAAK,CAAC;QAC7B,IAAI,UAAU,GAAQ,SAAS,CAAC;QAChC,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,GAAG,IAAI,CAAC;YACf,UAAU,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;SACvC;QACD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,UAAU,CAAC;SACnB;IACH,CAAC;IAGD,YAAY;QAEV,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAClC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC1B;QAED,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;CACF"}