# أيقونات MDPI (48x48)

هذا المجلد يحتاج إلى ملفات PNG للأيقونات:

## الملفات المطلوبة:
- `ic_launcher.png` (48x48 px)
- `ic_launcher_round.png` (48x48 px)

## الحل السريع:

### الطريقة الأولى: Android Studio (الأسهل)
1. Right-click على `res` folder
2. اختر `New` → `Image Asset`
3. اختر `Launcher Icons (Adaptive and Legacy)`
4. اختر `Foreground Layer` → `Clip Art`
5. ابحث عن "lock" واختر أيقونة القفل
6. غيّر الألوان:
   - Background Color: #667eea
   - Foreground Color: #FFFFFF
7. اضغط `Next` ثم `Finish`

### الطريقة الثانية: استخدام الأيقونة الموجودة
1. افتح `ic_launcher_simple.xml` في Android Studio
2. Right-click على الملف
3. اختر `Convert to PNG`
4. احفظ في جميع مجلدات mipmap

### الطريقة الثالثة: تحميل جاهزة
يمكنك تحميل أيقونات جاهزة من:
- https://romannurik.github.io/AndroidAssetStudio/
- https://appicon.co/

## ملاحظة:
بمجرد إضافة ملفات PNG، ستعمل الأيقونات بشكل مثالي!
