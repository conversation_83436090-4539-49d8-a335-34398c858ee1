<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
xmlns:android="http://schemas.android.com/apk/res/android"
           id="cordova-plugin-file"
      version="6.0.2">
    <name>File</name>
    <description>Cordova File Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,file</keywords>
    <repo>https://github.com/apache/cordova-plugin-file</repo>
    <issue>https://github.com/apache/cordova-plugin-file/issues</issue>

    <engines>
        <engine name="cordova-android" version=">=6.3.0" />
    </engines>

    <js-module src="www/DirectoryEntry.js" name="DirectoryEntry">
        <clobbers target="window.DirectoryEntry" />
    </js-module>

    <js-module src="www/DirectoryReader.js" name="DirectoryReader">
        <clobbers target="window.DirectoryReader" />
    </js-module>

    <js-module src="www/Entry.js" name="Entry">
        <clobbers target="window.Entry" />
    </js-module>

    <js-module src="www/File.js" name="File">
        <clobbers target="window.File" />
    </js-module>

    <js-module src="www/FileEntry.js" name="FileEntry">
        <clobbers target="window.FileEntry" />
    </js-module>

    <js-module src="www/FileError.js" name="FileError">
        <clobbers target="window.FileError" />
    </js-module>

    <js-module src="www/FileReader.js" name="FileReader">
        <clobbers target="window.FileReader" />
    </js-module>

    <js-module src="www/FileSystem.js" name="FileSystem">
        <clobbers target="window.FileSystem" />
    </js-module>

    <js-module src="www/FileUploadOptions.js" name="FileUploadOptions">
        <clobbers target="window.FileUploadOptions" />
    </js-module>

    <js-module src="www/FileUploadResult.js" name="FileUploadResult">
        <clobbers target="window.FileUploadResult" />
    </js-module>

    <js-module src="www/FileWriter.js" name="FileWriter">
        <clobbers target="window.FileWriter" />
    </js-module>

    <js-module src="www/Flags.js" name="Flags">
        <clobbers target="window.Flags" />
    </js-module>

    <js-module src="www/LocalFileSystem.js" name="LocalFileSystem">
        <!-- Non-standards way -->
        <clobbers target="window.LocalFileSystem" />
        <!-- Standards-compliant way -->
        <merges target="window" />
    </js-module>

    <js-module src="www/Metadata.js" name="Metadata">
        <clobbers target="window.Metadata" />
    </js-module>

    <js-module src="www/ProgressEvent.js" name="ProgressEvent">
        <clobbers target="window.ProgressEvent" />
    </js-module>

    <js-module src="www/fileSystems.js" name="fileSystems" />

    <js-module src="www/requestFileSystem.js" name="requestFileSystem">
        <clobbers target="window.requestFileSystem" />
    </js-module>

    <js-module src="www/resolveLocalFileSystemURI.js" name="resolveLocalFileSystemURI">
        <merges target="window" />
    </js-module>

    <!-- Required for browserify: we always link module below as there is conditional reference
    to this module from requestFileSystem and resolveLocalFileSystemURI modules. -->
    <js-module src="www/browser/isChrome.js" name="isChrome">
        <runs />
    </js-module>

    <!-- android -->
    <platform name="android">
        <info>
The Android Persistent storage location now defaults to "Internal". Please check this plugin's README to see if your application needs any changes in its config.xml.

If this is a new application no changes are required.

If this is an update to an existing application that did not specify an "AndroidPersistentFileLocation" you may need to add:

      "&lt;preference name="AndroidPersistentFileLocation" value="Compatibility" /&gt;"

to config.xml in order for the application to find previously stored files.
        </info>
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="File" >
                <param name="android-package" value="org.apache.cordova.file.FileUtils"/>
                <param name="onload" value="true" />
            </feature>
            <allow-navigation href="cdvfile:*" />
        </config-file>

        <config-file target="AndroidManifest.xml" parent="/*">
            <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
        </config-file>

        <source-file src="src/android/EncodingException.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/FileExistsException.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/InvalidModificationException.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/NoModificationAllowedException.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/TypeMismatchException.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/FileUtils.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/DirectoryManager.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/LocalFilesystemURL.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/Filesystem.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/LocalFilesystem.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/ContentFilesystem.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/AssetFilesystem.java" target-dir="src/org/apache/cordova/file" />
        <source-file src="src/android/PendingRequests.java" target-dir="src/org/apache/cordova/file" />

        <!-- android specific file apis -->
        <js-module src="www/android/FileSystem.js" name="androidFileSystem">
            <merges target="FileSystem" />
        </js-module>
        <js-module src="www/fileSystems-roots.js" name="fileSystems-roots">
            <runs/>
        </js-module>
        <js-module src="www/fileSystemPaths.js" name="fileSystemPaths">
            <merges target="cordova" />
            <runs/>
        </js-module>
    </platform>

    <!-- ios -->
    <platform name="ios">
        <config-file target="config.xml" parent="/*">
            <feature name="File">
                <param name="ios-package" value="CDVFile" />
                <param name="onload" value="true" />
            </feature>
        </config-file>
        <header-file src="src/ios/CDVFile.h" />
        <source-file src="src/ios/CDVFile.m" />
        <header-file src="src/ios/CDVLocalFilesystem.h" />
        <source-file src="src/ios/CDVLocalFilesystem.m" />
        <header-file src="src/ios/CDVAssetLibraryFilesystem.h" />
        <source-file src="src/ios/CDVAssetLibraryFilesystem.m" />

        <!-- ios specific file apis -->
        <js-module src="www/ios/FileSystem.js" name="iosFileSystem">
            <merges target="FileSystem" />
        </js-module>

        <js-module src="www/fileSystems-roots.js" name="fileSystems-roots">
            <runs/>
        </js-module>

        <js-module src="www/fileSystemPaths.js" name="fileSystemPaths">
            <merges target="cordova" />
            <runs/>
        </js-module>

        <framework src="AssetsLibrary.framework" />
        <framework src="MobileCoreServices.framework" />
    </platform>

    <!-- osx -->
    <platform name="osx">
        <config-file target="config.xml" parent="/*">
            <feature name="File">
                <param name="ios-package" value="CDVFile" />
                <param name="onload" value="true" />
            </feature>
        </config-file>
        <header-file src="src/osx/CDVFile.h" />
        <source-file src="src/osx/CDVFile.m" />
        <header-file src="src/osx/CDVLocalFilesystem.h" />
        <source-file src="src/osx/CDVLocalFilesystem.m" />

        <!-- osx specific file apis -->
        <js-module src="www/osx/FileSystem.js" name="osxFileSystem">
            <merges target="FileSystem" />
        </js-module>

        <js-module src="www/fileSystems-roots.js" name="fileSystems-roots">
            <runs/>
        </js-module>

        <js-module src="www/fileSystemPaths.js" name="fileSystemPaths">
            <merges target="cordova" />
            <runs/>
        </js-module>
    </platform>

    <!-- windows -->
    <platform name="windows">
        <js-module src="src/windows/FileProxy.js" name="FileProxy">
            <runs />
        </js-module>

        <js-module src="www/fileSystemPaths.js" name="fileSystemPaths">
            <merges target="cordova" />
            <runs/>
        </js-module>
    </platform>

    <platform name="browser">
        <!-- File for Chrome -->
        <js-module src="www/browser/Preparing.js" name="Preparing">
            <runs />
        </js-module>

        <js-module src="src/browser/FileProxy.js" name="browserFileProxy">
            <runs />
        </js-module>

        <js-module src="www/fileSystemPaths.js" name="fileSystemPaths">
            <merges target="cordova" />
            <runs />
        </js-module>

        <js-module src="www/browser/FileSystem.js" name="firefoxFileSystem">
            <merges target="window.FileSystem" />
        </js-module>
    </platform>

</plugin>
