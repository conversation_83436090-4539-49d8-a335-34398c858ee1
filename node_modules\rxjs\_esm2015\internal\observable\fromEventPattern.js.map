{"version": 3, "file": "fromEventPattern.js", "sources": ["../../../src/internal/observable/fromEventPattern.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AAwIvC,MAAM,UAAU,gBAAgB,CAAI,UAA8C,EAC9C,aAAiE,EACjE,cAAsC;IAExE,IAAI,cAAc,EAAE;QAElB,OAAO,gBAAgB,CAAI,UAAU,EAAE,aAAa,CAAC,CAAC,IAAI,CACxD,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAC5E,CAAC;KACH;IAED,OAAO,IAAI,UAAU,CAAU,UAAU,CAAC,EAAE;QAC1C,MAAM,OAAO,GAAG,CAAC,GAAG,CAAM,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1E,IAAI,QAAa,CAAC;QAClB,IAAI;YACF,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;SAChC;QAAC,OAAO,GAAG,EAAE;YACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;YAC9B,OAAO,SAAS,CAAC;SAClB;QAED,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAE;IACjD,CAAC,CAAC,CAAC;AACL,CAAC"}