// Generated by view binder compiler. Do not edit!
package com.karimwahib.passwordgenerator.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.karimwahib.passwordgenerator.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView appName;

  @NonNull
  public final TextView appSubtitle;

  @NonNull
  public final TextView developerInfo;

  @NonNull
  public final ImageView logo;

  @NonNull
  public final ProgressBar progressBar;

  private ActivitySplashBinding(@NonNull ConstraintLayout rootView, @NonNull TextView appName,
      @NonNull TextView appSubtitle, @NonNull TextView developerInfo, @NonNull ImageView logo,
      @NonNull ProgressBar progressBar) {
    this.rootView = rootView;
    this.appName = appName;
    this.appSubtitle = appSubtitle;
    this.developerInfo = developerInfo;
    this.logo = logo;
    this.progressBar = progressBar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_name;
      TextView appName = ViewBindings.findChildViewById(rootView, id);
      if (appName == null) {
        break missingId;
      }

      id = R.id.app_subtitle;
      TextView appSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (appSubtitle == null) {
        break missingId;
      }

      id = R.id.developer_info;
      TextView developerInfo = ViewBindings.findChildViewById(rootView, id);
      if (developerInfo == null) {
        break missingId;
      }

      id = R.id.logo;
      ImageView logo = ViewBindings.findChildViewById(rootView, id);
      if (logo == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      return new ActivitySplashBinding((ConstraintLayout) rootView, appName, appSubtitle,
          developerInfo, logo, progressBar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
