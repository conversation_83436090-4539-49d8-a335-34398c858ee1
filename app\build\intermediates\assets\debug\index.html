<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد كلمات مرور قوية</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Inter:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            min-height: 100vh;
            padding: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow-x: hidden;
        }

        body.en {
            font-family: 'Inter', sans-serif;
            direction: ltr;
        }

        .language-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .language-toggle:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 24px;
            width: 100%;
            max-width: 600px;
            padding: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 10px 0;
            overflow: hidden;
        }

        .tabs {
            display: flex;
            background: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab {
            flex: 1;
            padding: 16px 20px;
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab.active {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-bottom: 2px solid #667eea;
        }

        .tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.9);
        }

        .tab-content {
            display: none !important;
            padding: 24px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .tab-content.active {
            display: block !important;
            opacity: 1 !important;
        }
        
        header {
            text-align: center;
            margin-bottom: 20px;
        }

        header h1 {
            font-size: 24px;
            margin-bottom: 8px;
            color: #ffffff;
            font-weight: 700;
        }

        header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            line-height: 1.5;
        }

        .password-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 20px;
            position: relative;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .password-display:hover {
            border-color: rgba(255, 255, 255, 0.2);
            background: rgba(0, 0, 0, 0.4);
        }

        .password-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .password-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        .password-actions {
            display: flex;
            gap: 8px;
        }

        #password {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            letter-spacing: 1px;
            color: #ffffff;
            word-break: break-all;
            min-height: 50px;
            display: flex;
            align-items: center;
            font-family: 'Courier New', monospace;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        #password:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: #fff;
            width: 36px;
            height: 36px;
            border-radius: 10px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }
        
        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .option-group {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 16px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .length-group {
            grid-column: 1 / -1;
        }

        .option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .option:last-child {
            margin-bottom: 0;
        }

        .option label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .option label i {
            margin-left: 8px;
            margin-right: 8px;
            font-size: 16px;
            opacity: 0.8;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .slider-container label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            min-width: 120px;
        }

        #length {
            flex-grow: 1;
            height: 6px;
            -webkit-appearance: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            outline: none;
        }

        #length::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.5);
        }

        #length-value {
            background: rgba(255, 255, 255, 0.15);
            min-width: 40px;
            text-align: center;
            padding: 6px 10px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.1);
            transition: .3s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: #667eea;
            transition: .3s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: rgba(102, 126, 234, 0.3);
        }

        input:checked + .slider:before {
            transform: translateX(24px);
        }

        .strength-meter {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .strength-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .strength-header h3 {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        #strength-value {
            font-weight: 600;
            font-size: 14px;
        }

        .meter {
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .meter-fill {
            height: 100%;
            width: 0%;
            background: #ff1744;
            border-radius: 4px;
            transition: width 0.5s ease, background 0.5s ease;
        }
        
        .generate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            width: 100%;
            padding: 14px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        .generate-btn:active {
            transform: translateY(0);
        }

        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            display: none;
            animation: fadeInOut 3s;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .developer {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            text-align: center;
            margin-top: 10px;
            padding: 8px 16px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 16px;
            font-family: 'Courier New', monospace;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .developer:hover {
            color: rgba(255, 255, 255, 0.8);
            background: rgba(0, 0, 0, 0.3);
            transform: translateY(-1px);
        }

        .developer-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .developer span {
            font-weight: bold;
            color: #667eea;
        }

        .developer-icon {
            font-size: 14px;
            animation: pulse 2s infinite;
        }

        .whatsapp-contact {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: rgba(37, 211, 102, 0.2);
            border: 1px solid rgba(37, 211, 102, 0.3);
            border-radius: 20px;
            color: #25d366;
            text-decoration: none;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.3s ease;
            font-family: 'Inter', sans-serif;
        }

        .whatsapp-contact:hover {
            background: rgba(37, 211, 102, 0.3);
            border-color: rgba(37, 211, 102, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
        }

        .whatsapp-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }

        /* إخفاء العناصر بصرياً مع الحفاظ على إمكانية الوصول */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* محفظة كلمات المرور */
        .save-password-form {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .save-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            width: 100%;
            padding: 12px;
            font-size: 14px;
            font-weight: 600;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        .passwords-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .password-item {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .password-item:hover {
            background: rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .password-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .site-name {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .password-actions-list {
            display: flex;
            gap: 8px;
        }

        .password-item-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .password-field {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .password-field-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 4px;
        }

        .password-field-value {
            font-size: 14px;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            word-break: break-all;
        }

        .password-field.full-width {
            grid-column: 1 / -1;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .empty-state .material-icons {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .search-box {
            margin-bottom: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #ffffff;
            font-size: 14px;
        }

        .search-box {
            position: relative;
        }

        .search-box .material-icons {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            font-size: 20px;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateY(-20px); }
            10% { opacity: 1; transform: translateY(0); }
            90% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-20px); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @media (max-width: 600px) {
            .container {
                max-width: 95%;
                margin: 5px 0;
            }

            .tab-content {
                padding: 16px;
            }

            .options {
                grid-template-columns: 1fr;
            }

            .password-item-content {
                grid-template-columns: 1fr;
            }

            .language-toggle {
                top: 10px;
                right: 10px;
                padding: 6px 12px;
                font-size: 12px;
            }

            header h1 {
                font-size: 20px;
            }

            header p {
                font-size: 13px;
            }

            #password {
                font-size: 14px;
                padding: 12px;
            }

            .option label {
                font-size: 13px;
            }

            .slider-container label {
                min-width: 100px;
                font-size: 13px;
            }

            .developer {
                font-size: 11px;
                padding: 6px 12px;
            }

            .tab {
                padding: 12px 8px;
                font-size: 12px;
            }

            .tab span:not(.material-icons) {
                display: none;
            }

            .passwords-list {
                max-height: 300px;
            }

            .whatsapp-contact {
                font-size: 10px;
                padding: 4px 8px;
            }

            .whatsapp-icon {
                width: 14px;
                height: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="language-toggle" id="language-toggle">
        <span class="material-icons">language</span>
        <span id="lang-text">English</span>
    </div>

    <div class="container">
        <div class="tabs">
            <button class="tab active" data-tab="generator">
                <span class="material-icons">vpn_key</span>
                <span data-ar="مولد كلمات المرور" data-en="Password Generator">مولد كلمات المرور</span>
            </button>
            <button class="tab" data-tab="wallet">
                <span class="material-icons">account_balance_wallet</span>
                <span data-ar="محفظة كلمات المرور" data-en="Password Wallet">محفظة كلمات المرور</span>
            </button>
        </div>

        <div class="tab-content active" id="generator-tab">
            <header>
                <h1 data-ar="مولد كلمات مرور قوية" data-en="Strong Password Generator">مولد كلمات مرور قوية</h1>
                <p data-ar="أنشئ كلمات مرور آمنة وعشوائية تلبي جميع متطلبات الأمان المعقدة لحماية حساباتك" data-en="Generate secure and random passwords that meet all complex security requirements to protect your accounts">أنشئ كلمات مرور آمنة وعشوائية تلبي جميع متطلبات الأمان المعقدة لحماية حساباتك</p>
            </header>

        <div class="password-display">
            <div class="password-header">
                <span class="password-label" data-ar="كلمة المرور المُولدة" data-en="Generated Password">كلمة المرور المُولدة</span>
                <div class="password-actions">
                    <button class="action-btn" id="copy-btn" title="نسخ">
                        <span class="material-icons">content_copy</span>
                    </button>
                    <button class="action-btn" id="refresh-btn" title="تحديث">
                        <span class="material-icons">refresh</span>
                    </button>
                </div>
            </div>
            <div id="password" data-ar="انقر على الزر لإنشاء كلمة مرور" data-en="Click the button to generate a password">انقر على الزر لإنشاء كلمة مرور</div>
        </div>

        <div class="options">
            <div class="option-group length-group">
                <div class="slider-container">
                    <label for="length" data-ar="طول كلمة المرور:" data-en="Password Length:">طول كلمة المرور:</label>
                    <input type="range" id="length" min="8" max="32" value="16" title="اختر طول كلمة المرور" aria-label="طول كلمة المرور">
                    <div id="length-value">16</div>
                </div>
            </div>

            <div class="option-group">
                <div class="option">
                    <label for="uppercase"><i class="material-icons">text_fields</i> <span data-ar="أحرف كبيرة (A-Z)" data-en="Uppercase (A-Z)">أحرف كبيرة (A-Z)</span></label>
                    <label class="switch">
                        <input type="checkbox" id="uppercase" checked title="تضمين أحرف كبيرة" aria-label="أحرف كبيرة">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="option">
                    <label for="lowercase"><i class="material-icons">text_format</i> <span data-ar="أحرف صغيرة (a-z)" data-en="Lowercase (a-z)">أحرف صغيرة (a-z)</span></label>
                    <label class="switch">
                        <input type="checkbox" id="lowercase" checked title="تضمين أحرف صغيرة" aria-label="أحرف صغيرة">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <div class="option-group">
                <div class="option">
                    <label for="numbers"><i class="material-icons">pin</i> <span data-ar="أرقام (0-9)" data-en="Numbers (0-9)">أرقام (0-9)</span></label>
                    <label class="switch">
                        <input type="checkbox" id="numbers" checked title="تضمين أرقام" aria-label="أرقام">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="option">
                    <label for="symbols"><i class="material-icons">star</i> <span data-ar="رموز خاصة (@#$%)" data-en="Symbols (@#$%)">رموز خاصة (@#$%)</span></label>
                    <label class="switch">
                        <input type="checkbox" id="symbols" checked title="تضمين رموز خاصة" aria-label="رموز خاصة">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
        
        <div class="strength-meter">
            <div class="strength-header">
                <h3 data-ar="قوة كلمة المرور:" data-en="Password Strength:">قوة كلمة المرور:</h3>
                <div id="strength-value" data-ar="متوسط" data-en="Medium">متوسط</div>
            </div>
            <div class="meter">
                <div class="meter-fill" id="meter-fill"></div>
            </div>
        </div>

            <div style="display: flex; gap: 12px;">
                <button class="generate-btn" id="generate-btn" style="flex: 1;">
                    <span class="material-icons">autorenew</span>
                    <span data-ar="إنشاء كلمة مرور جديدة" data-en="Generate New Password">إنشاء كلمة مرور جديدة</span>
                </button>
                <button class="save-btn" id="save-to-wallet-btn" style="flex: 0 0 auto; width: auto; padding: 14px 20px;">
                    <span class="material-icons">save</span>
                    <span data-ar="حفظ في المحفظة" data-en="Save to Wallet">حفظ في المحفظة</span>
                </button>
            </div>
        </div>

        <div class="tab-content" id="wallet-tab">
            <header>
                <h1 data-ar="محفظة كلمات المرور" data-en="Password Wallet">محفظة كلمات المرور</h1>
                <p data-ar="احفظ وأدر كلمات المرور الخاصة بك بشكل آمن ومنظم" data-en="Save and manage your passwords securely and organized">احفظ وأدر كلمات المرور الخاصة بك بشكل آمن ومنظم</p>
            </header>

            <div class="save-password-form" id="save-form" style="display: none;">
                <div class="form-group">
                    <label data-ar="اسم الموقع أو التطبيق" data-en="Website or App Name">اسم الموقع أو التطبيق</label>
                    <input type="text" id="site-name" placeholder="مثال: فيسبوك، جوجل، بنك الأهلي..." data-placeholder-ar="مثال: فيسبوك، جوجل، بنك الأهلي..." data-placeholder-en="Example: Facebook, Google, Bank...">
                </div>
                <div class="form-group">
                    <label data-ar="اسم المستخدم أو البريد الإلكتروني" data-en="Username or Email">اسم المستخدم أو البريد الإلكتروني</label>
                    <input type="text" id="username" placeholder="<EMAIL>" data-placeholder-ar="<EMAIL>" data-placeholder-en="<EMAIL>">
                </div>
                <div class="form-group">
                    <label data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
                    <input type="text" id="password-to-save" readonly>
                </div>
                <div style="display: flex; gap: 12px;">
                    <button class="save-btn" id="confirm-save-btn">
                        <span class="material-icons">save</span>
                        <span data-ar="حفظ كلمة المرور" data-en="Save Password">حفظ كلمة المرور</span>
                    </button>
                    <button class="action-btn" id="cancel-save-btn" style="flex: 0 0 auto; background: rgba(255, 255, 255, 0.1);">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            </div>

            <div class="search-box">
                <label for="search-passwords" class="sr-only" data-ar="البحث في كلمات المرور" data-en="Search passwords">البحث في كلمات المرور</label>
                <span class="material-icons">search</span>
                <input type="text" id="search-passwords" placeholder="البحث في كلمات المرور..." data-placeholder-ar="البحث في كلمات المرور..." data-placeholder-en="Search passwords..." title="البحث في كلمات المرور المحفوظة" aria-label="البحث في كلمات المرور">
            </div>

            <div class="passwords-list" id="passwords-list">
                <div class="empty-state" id="empty-state">
                    <div class="material-icons">lock</div>
                    <h3 data-ar="لا توجد كلمات مرور محفوظة" data-en="No saved passwords">لا توجد كلمات مرور محفوظة</h3>
                    <p data-ar="ابدأ بإنشاء كلمة مرور وحفظها في المحفظة" data-en="Start by generating a password and saving it to the wallet">ابدأ بإنشاء كلمة مرور وحفظها في المحفظة</p>
                </div>
            </div>
        </div>
    </div>

    <div class="developer">
        <div class="developer-info">
            <i class="material-icons developer-icon">code</i>
            Developed by <span>Karim Wahib</span>
        </div>
        <a href="https://wa.me/201159296333?text=مرحباً، أعجبني تطبيق مولد كلمات المرور الخاص بك!" class="whatsapp-contact" target="_blank">
            <svg class="whatsapp-icon" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
            </svg>
            <span data-ar="تواصل معي عبر واتساب" data-en="Contact me on WhatsApp">تواصل معي عبر واتساب</span>
        </a>
    </div>

    <div class="notification" id="notification">تم نسخ كلمة المرور إلى الحافظة!</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // عناصر DOM
            const passwordEl = document.getElementById('password');
            const copyBtn = document.getElementById('copy-btn');
            const refreshBtn = document.getElementById('refresh-btn');
            const lengthSlider = document.getElementById('length');
            const lengthValue = document.getElementById('length-value');
            const uppercaseEl = document.getElementById('uppercase');
            const lowercaseEl = document.getElementById('lowercase');
            const numbersEl = document.getElementById('numbers');
            const symbolsEl = document.getElementById('symbols');
            const generateBtn = document.getElementById('generate-btn');
            const strengthValue = document.getElementById('strength-value');
            const meterFill = document.getElementById('meter-fill');
            const notification = document.getElementById('notification');
            const languageToggle = document.getElementById('language-toggle');
            const langText = document.getElementById('lang-text');

            // عناصر التبويبات
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            // عناصر المحفظة
            const saveToWalletBtn = document.getElementById('save-to-wallet-btn');
            const saveForm = document.getElementById('save-form');
            const siteNameInput = document.getElementById('site-name');
            const usernameInput = document.getElementById('username');
            const passwordToSaveInput = document.getElementById('password-to-save');
            const confirmSaveBtn = document.getElementById('confirm-save-btn');
            const cancelSaveBtn = document.getElementById('cancel-save-btn');
            const passwordsList = document.getElementById('passwords-list');
            const emptyState = document.getElementById('empty-state');
            const searchInput = document.getElementById('search-passwords');

            // متغير اللغة الحالية
            let currentLang = 'ar';

            // مصفوفة كلمات المرور المحفوظة
            let savedPasswords = JSON.parse(localStorage.getItem('savedPasswords')) || [];

            // نصوص الترجمة
            const translations = {
                ar: {
                    weak: 'ضعيف',
                    medium: 'متوسط',
                    strong: 'قوي',
                    veryStrong: 'قوي جداً',
                    copied: 'تم نسخ كلمة المرور إلى الحافظة!',
                    selectOption: 'الرجاء تحديد خيار واحد على الأقل!',
                    langButton: 'English',
                    passwordSaved: 'تم حفظ كلمة المرور بنجاح!',
                    fillAllFields: 'الرجاء ملء جميع الحقول!',
                    passwordDeleted: 'تم حذف كلمة المرور!',
                    confirmDelete: 'هل أنت متأكد من حذف كلمة المرور؟'
                },
                en: {
                    weak: 'Weak',
                    medium: 'Medium',
                    strong: 'Strong',
                    veryStrong: 'Very Strong',
                    copied: 'Password copied to clipboard!',
                    selectOption: 'Please select at least one option!',
                    langButton: 'العربية',
                    passwordSaved: 'Password saved successfully!',
                    fillAllFields: 'Please fill all fields!',
                    passwordDeleted: 'Password deleted!',
                    confirmDelete: 'Are you sure you want to delete this password?'
                }
            };

            // تبديل التبويبات - حل جذري
            function switchTab(tabName) {
                console.log('switchTab called with:', tabName);

                // طريقة مباشرة وقوية
                const allTabs = document.querySelectorAll('.tab');
                const allContents = document.querySelectorAll('.tab-content');

                // إزالة active من الجميع
                allTabs.forEach(tab => {
                    tab.classList.remove('active');
                });
                allContents.forEach(content => {
                    content.classList.remove('active');
                    content.style.display = 'none';
                });

                // إضافة active للمطلوب
                const targetTab = document.querySelector(`[data-tab="${tabName}"]`);
                const targetContent = document.getElementById(`${tabName}-tab`);

                console.log('Target tab:', targetTab);
                console.log('Target content:', targetContent);

                if (targetTab && targetContent) {
                    targetTab.classList.add('active');
                    targetContent.classList.add('active');
                    targetContent.style.display = 'block';
                    targetContent.style.opacity = '1';
                    console.log('Tab switched successfully to:', tabName);
                } else {
                    console.error('Could not find tab or content for:', tabName);
                }
            }

            // وظيفة مساعدة للانتقال المباشر للمحفظة
            function forceWalletTab() {
                console.log('Force switching to wallet...');

                // إخفاء تبويب المولد
                const generatorTab = document.querySelector('[data-tab="generator"]');
                const generatorContent = document.getElementById('generator-tab');
                if (generatorTab) generatorTab.classList.remove('active');
                if (generatorContent) {
                    generatorContent.classList.remove('active');
                    generatorContent.style.display = 'none';
                }

                // إظهار تبويب المحفظة
                const walletTab = document.querySelector('[data-tab="wallet"]');
                const walletContent = document.getElementById('wallet-tab');
                if (walletTab) walletTab.classList.add('active');
                if (walletContent) {
                    walletContent.classList.add('active');
                    walletContent.style.display = 'block';
                    walletContent.style.opacity = '1';
                }

                console.log('Wallet tab forced successfully');
            }

            // تبديل اللغة
            function toggleLanguage() {
                currentLang = currentLang === 'ar' ? 'en' : 'ar';
                document.body.className = currentLang === 'en' ? 'en' : '';
                document.documentElement.lang = currentLang;
                document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
                langText.textContent = translations[currentLang].langButton;

                // تحديث النصوص
                document.querySelectorAll('[data-ar][data-en]').forEach(element => {
                    element.textContent = element.getAttribute('data-' + currentLang);
                });

                // تحديث placeholders
                document.querySelectorAll('[data-placeholder-ar][data-placeholder-en]').forEach(element => {
                    element.placeholder = element.getAttribute('data-placeholder-' + currentLang);
                });

                // تحديث title وaria-label attributes
                updateAccessibilityAttributes();

                // تحديث العنوان
                document.title = currentLang === 'ar' ? 'مولد كلمات مرور قوية' : 'Strong Password Generator';

                // إعادة رسم قائمة كلمات المرور
                renderPasswordsList();
            }

        // تحديث خصائص إمكانية الوصول حسب اللغة
        function updateAccessibilityAttributes() {
            const accessibilityTexts = {
                ar: {
                    lengthSlider: 'اختر طول كلمة المرور',
                    lengthLabel: 'طول كلمة المرور',
                    uppercaseTitle: 'تضمين أحرف كبيرة',
                    uppercaseLabel: 'أحرف كبيرة',
                    lowercaseTitle: 'تضمين أحرف صغيرة',
                    lowercaseLabel: 'أحرف صغيرة',
                    numbersTitle: 'تضمين أرقام',
                    numbersLabel: 'أرقام',
                    symbolsTitle: 'تضمين رموز خاصة',
                    symbolsLabel: 'رموز خاصة',
                    searchTitle: 'البحث في كلمات المرور المحفوظة',
                    searchLabel: 'البحث في كلمات المرور'
                },
                en: {
                    lengthSlider: 'Choose password length',
                    lengthLabel: 'Password length',
                    uppercaseTitle: 'Include uppercase letters',
                    uppercaseLabel: 'Uppercase letters',
                    lowercaseTitle: 'Include lowercase letters',
                    lowercaseLabel: 'Lowercase letters',
                    numbersTitle: 'Include numbers',
                    numbersLabel: 'Numbers',
                    symbolsTitle: 'Include special symbols',
                    symbolsLabel: 'Special symbols',
                    searchTitle: 'Search saved passwords',
                    searchLabel: 'Search passwords'
                }
            };

            const texts = accessibilityTexts[currentLang];

            // تحديث slider
            const lengthSlider = document.getElementById('length');
            if (lengthSlider) {
                lengthSlider.title = texts.lengthSlider;
                lengthSlider.setAttribute('aria-label', texts.lengthLabel);
            }

            // تحديث checkboxes
            const checkboxes = [
                { id: 'uppercase', title: texts.uppercaseTitle, label: texts.uppercaseLabel },
                { id: 'lowercase', title: texts.lowercaseTitle, label: texts.lowercaseLabel },
                { id: 'numbers', title: texts.numbersTitle, label: texts.numbersLabel },
                { id: 'symbols', title: texts.symbolsTitle, label: texts.symbolsLabel }
            ];

            checkboxes.forEach(checkbox => {
                const element = document.getElementById(checkbox.id);
                if (element) {
                    element.title = checkbox.title;
                    element.setAttribute('aria-label', checkbox.label);
                }
            });

            // تحديث search input
            const searchInput = document.getElementById('search-passwords');
            if (searchInput) {
                searchInput.title = texts.searchTitle;
                searchInput.setAttribute('aria-label', texts.searchLabel);
            }
        }

            // تحديث قيمة طول كلمة المرور
            lengthSlider.addEventListener('input', function() {
                lengthValue.textContent = this.value;
                if (passwordEl.textContent !== passwordEl.getAttribute('data-' + currentLang)) {
                    updatePasswordStrength(passwordEl.textContent);
                }
            });
            
            // إنشاء كلمة مرور
            function generatePassword() {
                const length = lengthSlider.value;
                const includeUppercase = uppercaseEl.checked;
                const includeLowercase = lowercaseEl.checked;
                const includeNumbers = numbersEl.checked;
                const includeSymbols = symbolsEl.checked;

                // التحقق من تحديد خيار واحد على الأقل
                if (!includeUppercase && !includeLowercase && !includeNumbers && !includeSymbols) {
                    showNotification(translations[currentLang].selectOption);
                    return translations[currentLang].selectOption;
                }

                // مجموعات الأحرف
                const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                const lowercase = 'abcdefghijklmnopqrstuvwxyz';
                const numbers = '0123456789';
                const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

                // إنشاء مجموعة الأحرف المحددة
                let charSet = '';
                if (includeUppercase) charSet += uppercase;
                if (includeLowercase) charSet += lowercase;
                if (includeNumbers) charSet += numbers;
                if (includeSymbols) charSet += symbols;

                // توليد كلمة المرور
                let password = '';
                for (let i = 0; i < length; i++) {
                    const randomIndex = Math.floor(Math.random() * charSet.length);
                    password += charSet[randomIndex];
                }

                // تحديث قوة كلمة المرور
                updatePasswordStrength(password);

                return password;
            }
            
            // تحديث قوة كلمة المرور
            function updatePasswordStrength(password) {
                let strength = 0;

                // طول كلمة المرور
                if (password.length >= 12) strength += 30;
                else if (password.length >= 8) strength += 15;

                // تنوع الأحرف
                const hasUppercase = /[A-Z]/.test(password);
                const hasLowercase = /[a-z]/.test(password);
                const hasNumbers = /[0-9]/.test(password);
                const hasSymbols = /[^A-Za-z0-9]/.test(password);

                if (hasUppercase) strength += 15;
                if (hasLowercase) strength += 15;
                if (hasNumbers) strength += 15;
                if (hasSymbols) strength += 15;

                // تحديث واجهة المستخدم
                meterFill.style.width = strength + '%';

                if (strength < 30) {
                    strengthValue.textContent = translations[currentLang].weak;
                    meterFill.style.background = '#ff1744';
                } else if (strength < 70) {
                    strengthValue.textContent = translations[currentLang].medium;
                    meterFill.style.background = '#ff9800';
                } else if (strength < 90) {
                    strengthValue.textContent = translations[currentLang].strong;
                    meterFill.style.background = '#4caf50';
                } else {
                    strengthValue.textContent = translations[currentLang].veryStrong;
                    meterFill.style.background = '#00e676';
                }
            }
            
            // نسخ كلمة المرور
            function copyPassword() {
                const defaultMessages = [
                    passwordEl.getAttribute('data-ar'),
                    passwordEl.getAttribute('data-en'),
                    translations.ar.selectOption,
                    translations.en.selectOption
                ];

                if (defaultMessages.includes(passwordEl.textContent)) {
                    return;
                }

                navigator.clipboard.writeText(passwordEl.textContent)
                    .then(() => {
                        showNotification(translations[currentLang].copied);
                    })
                    .catch(err => {
                        console.error('فشل في النسخ: ', err);
                    });
            }

            // إظهار الإشعار
            function showNotification(message) {
                notification.textContent = message;
                notification.style.display = 'block';

                setTimeout(() => {
                    notification.style.display = 'none';
                }, 3000);
            }

            // حفظ كلمة المرور في المحفظة
            function savePasswordToWallet() {
                const currentPassword = passwordEl.textContent;
                const defaultMessages = [
                    passwordEl.getAttribute('data-ar'),
                    passwordEl.getAttribute('data-en'),
                    translations.ar.selectOption,
                    translations.en.selectOption
                ];

                if (defaultMessages.includes(currentPassword)) {
                    showNotification(translations[currentLang].selectOption);
                    return;
                }

                passwordToSaveInput.value = currentPassword;
                saveForm.style.display = 'block';
                siteNameInput.focus();
            }

            // تأكيد حفظ كلمة المرور
            function confirmSavePassword() {
                const siteName = siteNameInput.value.trim();
                const username = usernameInput.value.trim();
                const password = passwordToSaveInput.value;

                if (!siteName || !username || !password) {
                    showNotification(translations[currentLang].fillAllFields);
                    return;
                }

                const passwordEntry = {
                    id: Date.now(),
                    siteName: siteName,
                    username: username,
                    password: password,
                    createdAt: new Date().toISOString()
                };

                savedPasswords.push(passwordEntry);
                localStorage.setItem('savedPasswords', JSON.stringify(savedPasswords));

                // إعادة تعيين النموذج
                siteNameInput.value = '';
                usernameInput.value = '';
                passwordToSaveInput.value = '';
                saveForm.style.display = 'none';

                showNotification(translations[currentLang].passwordSaved);
                renderPasswordsList();

                // الانتقال إلى تبويب المحفظة - حل جذري
                console.log('About to switch to wallet tab...');

                // استخدام الوظيفة المباشرة
                forceWalletTab();

                // تأكيد إضافي بعد تأخير قصير
                setTimeout(() => {
                    forceWalletTab();
                    console.log('Double-checked wallet tab switch');
                }, 50);
            }

            // إلغاء حفظ كلمة المرور
            function cancelSavePassword() {
                siteNameInput.value = '';
                usernameInput.value = '';
                passwordToSaveInput.value = '';
                saveForm.style.display = 'none';
            }

            // رسم قائمة كلمات المرور
            function renderPasswordsList() {
                const searchTerm = searchInput.value.toLowerCase();
                const filteredPasswords = savedPasswords.filter(item =>
                    item.siteName.toLowerCase().includes(searchTerm) ||
                    item.username.toLowerCase().includes(searchTerm)
                );

                if (filteredPasswords.length === 0) {
                    passwordsList.innerHTML = `
                        <div class="empty-state">
                            <div class="material-icons">lock</div>
                            <h3 data-ar="لا توجد كلمات مرور محفوظة" data-en="No saved passwords">${currentLang === 'ar' ? 'لا توجد كلمات مرور محفوظة' : 'No saved passwords'}</h3>
                            <p data-ar="ابدأ بإنشاء كلمة مرور وحفظها في المحفظة" data-en="Start by generating a password and saving it to the wallet">${currentLang === 'ar' ? 'ابدأ بإنشاء كلمة مرور وحفظها في المحفظة' : 'Start by generating a password and saving it to the wallet'}</p>
                        </div>
                    `;
                    return;
                }

                passwordsList.innerHTML = filteredPasswords.map(item => `
                    <div class="password-item">
                        <div class="password-item-header">
                            <div class="site-name">${item.siteName}</div>
                            <div class="password-actions-list">
                                <button class="action-btn" onclick="copyPasswordFromWallet('${item.password}')" title="${currentLang === 'ar' ? 'نسخ كلمة المرور' : 'Copy Password'}">
                                    <span class="material-icons">content_copy</span>
                                </button>
                                <button class="action-btn" onclick="deletePassword(${item.id})" title="${currentLang === 'ar' ? 'حذف' : 'Delete'}" style="background: rgba(255, 23, 68, 0.2);">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        </div>
                        <div class="password-item-content">
                            <div class="password-field">
                                <div class="password-field-label">${currentLang === 'ar' ? 'اسم المستخدم' : 'Username'}</div>
                                <div class="password-field-value">${item.username}</div>
                            </div>
                            <div class="password-field">
                                <div class="password-field-label">${currentLang === 'ar' ? 'كلمة المرور' : 'Password'}</div>
                                <div class="password-field-value">••••••••</div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            // نسخ كلمة المرور من المحفظة
            function copyPasswordFromWallet(password) {
                navigator.clipboard.writeText(password)
                    .then(() => {
                        showNotification(translations[currentLang].copied);
                    })
                    .catch(err => {
                        console.error('فشل في النسخ: ', err);
                    });
            }

            // حذف كلمة المرور
            function deletePassword(id) {
                if (confirm(translations[currentLang].confirmDelete)) {
                    savedPasswords = savedPasswords.filter(item => item.id !== id);
                    localStorage.setItem('savedPasswords', JSON.stringify(savedPasswords));
                    showNotification(translations[currentLang].passwordDeleted);
                    renderPasswordsList();
                }
            }

            // جعل الوظائف متاحة عالمياً
            window.copyPasswordFromWallet = copyPasswordFromWallet;
            window.deletePassword = deletePassword;
            window.switchTab = switchTab;
            window.forceWalletTab = forceWalletTab;

            // وظيفة اختبار للتبويبات
            window.testTabSwitch = function() {
                console.log('Testing tab switch...');
                console.log('Current tabs:', document.querySelectorAll('.tab'));
                console.log('Current contents:', document.querySelectorAll('.tab-content'));
                forceWalletTab();
            };

            // إنشاء كلمة مرور عند النقر على الزر
            generateBtn.addEventListener('click', function() {
                passwordEl.textContent = generatePassword();
            });

            // تحديث كلمة المرور عند النقر على زر التحديث
            refreshBtn.addEventListener('click', function() {
                passwordEl.textContent = generatePassword();
            });

            // نسخ كلمة المرور عند النقر على زر النسخ
            copyBtn.addEventListener('click', copyPassword);

            // تبديل اللغة
            languageToggle.addEventListener('click', toggleLanguage);

            // التبويبات
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabName = this.getAttribute('data-tab');
                    switchTab(tabName);
                });
            });

            // وظائف المحفظة
            saveToWalletBtn.addEventListener('click', savePasswordToWallet);
            confirmSaveBtn.addEventListener('click', confirmSavePassword);
            cancelSaveBtn.addEventListener('click', cancelSavePassword);
            searchInput.addEventListener('input', renderPasswordsList);

            // تحديث قوة كلمة المرور عند تغيير الخيارات
            [uppercaseEl, lowercaseEl, numbersEl, symbolsEl].forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    if (passwordEl.textContent !== passwordEl.getAttribute('data-' + currentLang)) {
                        updatePasswordStrength(passwordEl.textContent);
                    }
                });
            });

            // تحديث placeholders عند التحميل
            document.querySelectorAll('[data-placeholder-ar][data-placeholder-en]').forEach(element => {
                element.placeholder = element.getAttribute('data-placeholder-' + currentLang);
            });

            // تحديث خصائص إمكانية الوصول عند التحميل
            updateAccessibilityAttributes();

            // إنشاء كلمة مرور أولية عند التحميل
            passwordEl.textContent = generatePassword();

            // رسم قائمة كلمات المرور عند التحميل
            renderPasswordsList();
        });
    </script>
</body>
</html>