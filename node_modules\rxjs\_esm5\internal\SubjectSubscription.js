/** PURE_IMPORTS_START tslib,_Subscription PURE_IMPORTS_END */
import * as tslib_1 from "tslib";
import { Subscription } from './Subscription';
var SubjectSubscription = /*@__PURE__*/ (function (_super) {
    tslib_1.__extends(SubjectSubscription, _super);
    function SubjectSubscription(subject, subscriber) {
        var _this = _super.call(this) || this;
        _this.subject = subject;
        _this.subscriber = subscriber;
        _this.closed = false;
        return _this;
    }
    SubjectSubscription.prototype.unsubscribe = function () {
        if (this.closed) {
            return;
        }
        this.closed = true;
        var subject = this.subject;
        var observers = subject.observers;
        this.subject = null;
        if (!observers || observers.length === 0 || subject.isStopped || subject.closed) {
            return;
        }
        var subscriberIndex = observers.indexOf(this.subscriber);
        if (subscriberIndex !== -1) {
            observers.splice(subscriberIndex, 1);
        }
    };
    return SubjectSubscription;
}(Subscription));
export { SubjectSubscription };
//# sourceMappingURL=SubjectSubscription.js.map
