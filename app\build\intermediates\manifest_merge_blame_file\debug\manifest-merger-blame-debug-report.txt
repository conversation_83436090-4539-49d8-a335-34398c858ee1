1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.karimwahib.passwordgenerator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:6:5-66
12-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:6:22-63
13
14    <permission
14-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:8:5-47:19
21        android:allowBackup="true"
21-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="true"
26        android:fullBackupContent="@xml/backup_rules"
26-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@android:drawable/ic_lock_idle_lock"
27-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:12:9-59
28        android:label="@string/app_name"
28-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@android:drawable/ic_lock_idle_lock"
29-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:14:9-64
30        android:supportsRtl="true"
30-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.PasswordGeneratorPro"
31-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:16:9-58
32        android:usesCleartextTraffic="true" >
32-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:17:9-44
33
34        <!-- دعم الشاشات المختلفة -->
35        <meta-data
35-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:21:9-23:36
36            android:name="android.webkit.WebView.MetricsOptOut"
36-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:22:13-64
37            android:value="true" />
37-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:23:13-33
38        <meta-data
38-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:24:9-26:37
39            android:name="android.webkit.WebView.EnableSafeBrowsing"
39-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:25:13-69
40            android:value="false" />
40-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:26:13-34
41
42        <activity
42-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:28:9-37:20
43            android:name="com.karimwahib.passwordgenerator.MainActivity"
43-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:29:13-41
44            android:exported="true"
44-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:30:13-36
45            android:screenOrientation="portrait"
45-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:31:13-49
46            android:theme="@style/Theme.PasswordGeneratorPro.NoActionBar" >
46-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:32:13-74
47            <intent-filter>
47-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:33:13-36:29
48                <action android:name="android.intent.action.MAIN" />
48-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:34:17-69
48-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:34:25-66
49
50                <category android:name="android.intent.category.LAUNCHER" />
50-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:35:17-77
50-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:35:27-74
51            </intent-filter>
52        </activity>
53
54        <!-- Splash Screen Activity -->
55        <activity
55-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:40:9-45:20
56            android:name="com.karimwahib.passwordgenerator.SplashActivity"
56-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:41:13-43
57            android:exported="true"
57-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:42:13-36
58            android:screenOrientation="portrait"
58-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:43:13-49
59            android:theme="@style/SplashTheme" >
59-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:44:13-47
60        </activity>
61
62        <provider
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.karimwahib.passwordgenerator.androidx-startup"
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <receiver
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
78            android:name="androidx.profileinstaller.ProfileInstallReceiver"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
79            android:directBootAware="false"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
80            android:enabled="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
81            android:exported="true"
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
82            android:permission="android.permission.DUMP" >
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
84                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
87                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
90                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
93                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
94            </intent-filter>
95        </receiver>
96    </application>
97
98</manifest>
