1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.karimwahib.passwordgenerator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:5:5-67
11-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:6:5-66
12-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:6:22-63
13
14    <permission
14-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.karimwahib.passwordgenerator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:8:5-39:19
21        android:allowBackup="true"
21-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\07e75e0a705b1dc588bf475ef30dcfa5\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="true"
26        android:fullBackupContent="@xml/backup_rules"
26-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.PasswordGeneratorPro"
31-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:16:9-58
32        android:usesCleartextTraffic="true" >
32-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:17:9-44
33        <activity
33-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:20:9-29:20
34            android:name="com.karimwahib.passwordgenerator.MainActivity"
34-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:21:13-41
35            android:exported="true"
35-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:22:13-36
36            android:screenOrientation="portrait"
36-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:23:13-49
37            android:theme="@style/Theme.PasswordGeneratorPro.NoActionBar" >
37-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:24:13-74
38            <intent-filter>
38-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:25:13-28:29
39                <action android:name="android.intent.action.MAIN" />
39-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:26:17-69
39-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:26:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:27:17-77
41-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:27:27-74
42            </intent-filter>
43        </activity>
44
45        <!-- Splash Screen Activity -->
46        <activity
46-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:32:9-37:20
47            android:name="com.karimwahib.passwordgenerator.SplashActivity"
47-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:33:13-43
48            android:exported="true"
48-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:34:13-36
49            android:screenOrientation="portrait"
49-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:35:13-49
50            android:theme="@style/SplashTheme" >
50-->E:\my project program\update\password genretor\app\src\main\AndroidManifest.xml:36:13-47
51        </activity>
52
53        <provider
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.karimwahib.passwordgenerator.androidx-startup"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be942dd44f608c746aff5ea5224856fa\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\289acb454c1c88046b2c2c7f78540e8a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\787ccc09c46975528ef23cc35b6f34cd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
