{"version": 3, "file": "audit.js", "sources": ["../../../src/internal/operators/audit.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AAgDjG,MAAM,UAAU,KAAK,CAAI,gBAA0D;IACjF,OAAO,SAAS,qBAAqB,CAAC,MAAqB;QACzD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,aAAa;IACjB,YAAoB,gBAA0D;QAA1D,qBAAgB,GAAhB,gBAAgB,CAA0C;IAC9E,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,eAAe,CAAO,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACxF,CAAC;CACF;AAOD,MAAM,eAAsB,SAAQ,qBAA2B;IAM7D,YAAY,WAA0B,EAClB,gBAA0D;QAC5E,KAAK,CAAC,WAAW,CAAC,CAAC;QADD,qBAAgB,GAAhB,gBAAgB,CAA0C;QAJtE,aAAQ,GAAY,KAAK,CAAC;IAMlC,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,QAAQ,CAAC;YACb,IAAI;gBACF,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC;gBAClC,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;aACpC;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC;aACrC;YACD,MAAM,iBAAiB,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;YACpF,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAClD,IAAI,CAAC,aAAa,EAAE,CAAC;aACtB;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC,CAAC;aAC9C;SACF;IACH,CAAC;IAED,aAAa;QACX,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAC5C,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,SAAS,CAAC,WAAW,EAAE,CAAC;SACzB;QACD,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,KAAK,CAAC,CAAC;SAC/B;IACH,CAAC;IAED,UAAU;QACR,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;CACF"}