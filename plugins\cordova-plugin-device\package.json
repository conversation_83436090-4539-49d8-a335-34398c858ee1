{"name": "cordova-plugin-device", "version": "2.1.0", "description": "Cordova Device Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-device", "platforms": ["android", "electron", "ios", "windows", "browser", "osx"]}, "repository": "github:apache/cordova-plugin-device", "bugs": "https://github.com/apache/cordova-plugin-device/issues", "keywords": ["<PERSON><PERSON>", "device", "ecosystem:cordova", "cordova-android", "cordova-electron", "cordova-ios", "cordova-windows", "cordova-browser", "cordova-osx"], "scripts": {"test": "npm run lint", "lint": "eslint ."}, "author": "Apache Software Foundation", "license": "Apache-2.0", "engines": {"cordovaDependencies": {"3.0.0": {"cordova": ">100", "cordova-electron": ">=3.0.0"}}}, "devDependencies": {"@cordova/eslint-config": "^3.0.0"}}